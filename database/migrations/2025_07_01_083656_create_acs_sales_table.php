<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class() extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('acs_sales', function (Blueprint $table) {
            $table->ulid('id')->primary(); // Changed from id() to ulid() - from migration 2025_08_06_161724
            $table->integer('23_senarai_jualan_id');
            $table->foreignUlid('acs_agent_user_id')->constrained('acs_users', 'id')->onDelete(null);
            $table->bigInteger('acs_coorperative_id')->constrained('acs_coorperative', 'id')->onDelete(null);
            $table->bigInteger('acs_coorperative_branch_id')->constrained('acs_coorperative_branch', 'id')->onDelete(null);
            $table->string('sku_number')->nullable();
            $table->decimal('total_price', 15, 2);
            $table->unsignedBigInteger('cents')->nullable();
            $table->string('variety_type');
            $table->string('purity')->nullable();
            $table->string('design_code')->nullable();
            $table->string('design_name')->nullable();
            $table->string('description')->nullable();
            $table->string('customer_name')->nullable();
            $table->string('email_customer')->nullable();
            $table->integer('status')->default(0);
            $table->timestamps();
            $table->softDeletes();

            // Add indexes for better performance
            $table->index('23_senarai_jualan_id');
            $table->index('acs_agent_user_id');
            $table->index('acs_coorperative_id');
            $table->index('acs_coorperative_branch_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('acs_sales');
    }
};
