<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class() extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('acs_comission_settings', function (Blueprint $table) {
            $table->id();
            //important notes
            //this attribute must be match 1=1 with gcomm intergration categories
            $table->string('variety_type')->nullable();
            $table->string('product_category_code')->nullable();
            $table->string('product_category_name'); // Added from migration 2025_07_31_163213
            $table->string('variety_name'); // Added from migration 2025_07_31_163213
            //end here
            $table->text('description')->nullable();
            $table->enum('status', ['active', 'inactive'])->default('active');
            $table->text('design_code')->nullable();
            $table->text('variety_code')->nullable();
            $table->ulid('created_by')->nullable();
            $table->ulid('updated_by')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('acs_comission_settings');
    }
};
