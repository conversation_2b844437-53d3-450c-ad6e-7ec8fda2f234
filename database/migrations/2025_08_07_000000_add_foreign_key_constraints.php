<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add foreign key constraints for acs_coorperative_branch table
        Schema::table('acs_coorperative_branch', function (Blueprint $table) {
            $table->foreign('created_by')->references('id')->on('acs_users')->onDelete('set null');
            $table->foreign('verified_by')->references('id')->on('acs_users')->onDelete('set null');
            $table->foreign('acs_bank_names_id')->references('id')->on('acs_bank_names')->onDelete('set null');
        });

        // Add foreign key constraint for acs_users_details table
        Schema::table('acs_users_details', function (Blueprint $table) {
            $table->foreign('acs_bank_detail_id')->references('id')->on('acs_bank_details')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Drop foreign key constraints for acs_coorperative_branch table
        Schema::table('acs_coorperative_branch', function (Blueprint $table) {
            $table->dropForeign(['created_by']);
            $table->dropForeign(['verified_by']);
            $table->dropForeign(['acs_bank_names_id']);
        });

        // Drop foreign key constraint for acs_users_details table
        Schema::table('acs_users_details', function (Blueprint $table) {
            $table->dropForeign(['acs_bank_detail_id']);
        });
    }
};
