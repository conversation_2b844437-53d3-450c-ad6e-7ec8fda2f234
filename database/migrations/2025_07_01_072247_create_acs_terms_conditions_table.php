<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('acs_terms_conditions', function (Blueprint $table) {
            $table->id();
            $table->string('title');
            $table->foreignId('acs_role_id')->constrained('acs_roles');
            $table->enum('main_agent_type', ['all', 'specific'])->default('all'); // Added from migration 2025_07_27_041443
            $table->string('main_agent_email')->nullable(); // Added from migration 2025_07_27_041443
            $table->foreignUlid('main_agent_id')->nullable()->constrained('acs_users')->onDelete('cascade'); // Added from migration 2025_07_27_041443
            $table->string('content', 500)->nullable();
            $table->string('file_path')->nullable();
            $table->integer('status');
            $table->index(['acs_role_id', 'status'], 'idx_role_status');
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('acs_terms_conditions');
    }
};
