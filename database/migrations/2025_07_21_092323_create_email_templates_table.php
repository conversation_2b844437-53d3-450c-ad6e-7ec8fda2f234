<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('acs_email_templates', function (Blueprint $table) {
            $table->id();
            $table->string('type')->unique(); // e.g., 'agent_activation'
            $table->string('subject');
            $table->text('body');
            $table->string('button_text')->nullable(); // Added from migration 2025_07_25_044959
            $table->enum('button_position', ['top', 'middle', 'bottom', 'none'])->default('middle'); // Added from migrations 2025_07_26_000000 and 2025_07_28_161437
            $table->text('available_placeholders')->nullable(); // JSON array of available placeholders
            $table->boolean('is_active')->default(true);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('acs_email_templates');
    }
};
