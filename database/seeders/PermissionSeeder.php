<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class PermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Reset cached roles and permissions
        app()[\Spatie\Permission\PermissionRegistrar::class]->forgetCachedPermissions();

        // Create permissions
        $permissions = [
            // Dashboard permissions
            'view dashboard',
            'edit dashboard',

            // Profile permissions
            'view profile',
            'edit profile',

            // Agent management permissions
            'view agents',
            'edit agents',
            'register agents',
            'manage agent kyc',

            // Report permissions
            'view reports',
            'edit reports',
            'export reports',

            // Agreement permissions
            'view agreements',
            'edit agreements',
            'approve agreements',

            // Bank account permissions
            'view bank_account',
            'edit bank_account',

            // Sub-main-agent management
            'register sub_main_agents',
            'manage sub_main_agent_permissions',
        ];

        foreach ($permissions as $permission) {
            Permission::create(['name' => $permission]);
        }

        // Create roles and assign permissions

        // Super Admin - has all permissions
        $superAdminRole = Role::create(['name' => 'super-admin']);
        $superAdminRole->givePermissionTo(Permission::all());

        // Admin - has all permissions except super admin specific ones
        $adminRole = Role::create(['name' => 'admin']);
        $adminRole->givePermissionTo([
            'view dashboard',
            'edit dashboard',
            'view profile',
            'edit profile',
            'view agents',
            'edit agents',
            'register agents',
            'manage agent kyc',
            'view reports',
            'edit reports',
            'export reports',
            'view agreements',
            'edit agreements',
            'approve agreements',
            'view bank_account',
            'edit bank_account',
        ]);

        // Main Agent - has most permissions
        $mainAgentRole = Role::create(['name' => 'main-agent']);
        $mainAgentRole->givePermissionTo([
            'view dashboard',
            'edit dashboard',
            'view profile',
            'edit profile',
            'view agents',
            'edit agents',
            'register agents',
            'view reports',
            'edit reports',
            'view agreements',
            'edit agreements',
            'view bank_account',
            'edit bank_account',
            'register sub_main_agents',
            'manage sub_main_agent_permissions',
        ]);

        // Sub-Main-Agent - permissions will be set individually by main agents
        $subMainAgentRole = Role::create(['name' => 'sub-main-agent']);
        // No default permissions - these will be managed through AcsUserPermission

        // Agent - basic permissions
        $agentRole = Role::create(['name' => 'agent']);
        $agentRole->givePermissionTo([
            'view dashboard',
            'view profile',
            'edit profile',
            'view reports',
            'view agreements',
            'view bank_account',
            'edit bank_account',
        ]);

        $this->command->info('Permissions and roles seeded successfully!');
    }
}
