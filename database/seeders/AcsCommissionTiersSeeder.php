<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\AcsCommissionTier;

class AcsCommissionTiersSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $commissionTiers = [
            [
                'commission_setting_id' => 1,
                'min_qty' => 1,
                'max_qty' => 10,
                'commission_percentage' => 2.00,
                'sort_order' => 1,
            ],
            [
                'commission_setting_id' => 1,
                'min_qty' => 11,
                'max_qty' => 20,
                'commission_percentage' => 1.89,
                'sort_order' => 2,
            ],
            [
                'commission_setting_id' => 1,
                'min_qty' => 21,
                'max_qty' => 30,
                'commission_percentage' => 1.50,
                'sort_order' => 3,
            ],
            [
                'commission_setting_id' => 2,
                'min_qty' => 1,
                'max_qty' => 10,
                'commission_percentage' => 2.00,
                'sort_order' => 1,
            ],
            [
                'commission_setting_id' => 2,
                'min_qty' => 11,
                'max_qty' => 20,
                'commission_percentage' => 1.89,
                'sort_order' => 2,
            ],
            [
                'commission_setting_id' => 2,
                'min_qty' => 21,
                'max_qty' => 30,
                'commission_percentage' => 1.50,
                'sort_order' => 3,
            ],
            [
                'commission_setting_id' => 3,
                'min_qty' => 1,
                'max_qty' => 10,
                'commission_percentage' => 2.00,
                'sort_order' => 1,
            ],
            [
                'commission_setting_id' => 3,
                'min_qty' => 11,
                'max_qty' => 20,
                'commission_percentage' => 1.89,
                'sort_order' => 2,
            ],
        ];

        foreach ($commissionTiers as $tier) {
            AcsCommissionTier::create($tier);
        }
    }
}
