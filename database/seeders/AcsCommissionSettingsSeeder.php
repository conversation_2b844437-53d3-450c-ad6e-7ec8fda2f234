<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\AcsCommissionSetting;

class AcsCommissionSettingsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $commissionSettings = [
            [
                'variety_type' => '7',
                'product_category_code' => '588',
                'product_category_name' => 'BE',
                'variety_name' => 'KOLEKSI BLACK EDITION | 999.9 GOLD WAFER (5.00G)',
                'description' => null,
                'status' => 'active',
                'design_code' => null,
                'variety_code' => null,
                'created_by' => '01k1bgarc7gbxvhn6v4czh8a73',
                'updated_by' => '01k1bgarc7gbxvhn6v4czh8a73',
            ],
            [
                'variety_type' => '8',
                'product_category_code' => '588',
                'product_category_name' => 'BE',
                'variety_name' => 'KOLEKSI BLACK EDITION | 999.9 GOLD WAFER (10.00G)',
                'description' => null,
                'status' => 'active',
                'design_code' => null,
                'variety_code' => null,
                'created_by' => '01k1bgarc7gbxvhn6v4czh8a73',
                'updated_by' => '01k1bgarc7gbxvhn6v4czh8a73',
            ],
            [
                'variety_type' => '18',
                'product_category_code' => '588',
                'product_category_name' => 'BE',
                'variety_name' => 'KOLEKSI BLACK EDITION | 999.9 GOLD WAFER (3.00G)',
                'description' => null,
                'status' => 'active',
                'design_code' => null,
                'variety_code' => null,
                'created_by' => '01k1bgarc7gbxvhn6v4czh8a73',
                'updated_by' => '01k1bgarc7gbxvhn6v4czh8a73',
            ],
        ];

        foreach ($commissionSettings as $setting) {
            AcsCommissionSetting::create($setting);
        }
    }
}
