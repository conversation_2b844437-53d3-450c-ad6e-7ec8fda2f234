<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;
use App\Models\AcsSales;
use App\Models\AcsUser;

class AcsSalesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get agent role ID
        $agentRole = DB::table('acs_roles')
            ->where('name', 'agent')
            ->first();

        if (!$agentRole) {
            $this->command->error('Agent role not found. Please run AcsRoleSeeder first.');
            return;
        }

        // Get all agent users
        $agentUsers = AcsUser::where('acs_role_id', $agentRole->id)
            ->where('status', 'active')
            ->get();

        if ($agentUsers->isEmpty()) {
            $this->command->error('No active agent users found. Please run AgentOnlySeeder first.');
            return;
        }

        $this->command->info("Found {$agentUsers->count()} agent users for sales data.");

        // Sample sales data
        $salesData = [
            [
                'variety_type' => '7',
                'sku_number' => 'SKU-GR916-001',
                'total_price' => 2500.00,
            ],
            [
                'variety_type' => '7',
                'sku_number' => 'SKU-GN999-001',
                'total_price' => 5500.00,
            ],
            [
                'variety_type' => '8',
                'sku_number' => 'SKU-GB916-001',
                'total_price' => 1800.00,
            ],
            [
                'variety_type' => '8',
                'sku_number' => 'SKU-GE999-001',
                'total_price' => 3200.00,
            ],
            [
                'variety_type' => '8',
                'sku_number' => 'SKU-GC916-001',
                'total_price' => 4100.00,
            ],
            [
                'variety_type' => '8',
                'sku_number' => 'SKU-GP999-001',
                'total_price' => 2800.00,
            ],
        ];

        $createdSales = [];
        $saleId = 1;

        // Create sales records for each agent
        foreach ($agentUsers as $agent) {
            foreach ($salesData as $index => $saleData) {
                $sale = AcsSales::create([
                    '23_senarai_jualan_id' => $saleId,
                    'acs_agent_user_id' => $agent->id,
                    'acs_coorperative_id' => $agent->acs_coorperative_id,
                    'acs_coorperative_branch_id' => $agent->acs_coorperative_branch_id,
                    'sku_number' => $saleData['sku_number'],
                    'total_price' => $saleData['total_price'],
                    'variety_type' => $saleData['variety_type'],
                ]);

                $createdSales[] = $sale;
                $saleId++;
            }
        }

        $this->command->info("✅ Created " . count($createdSales) . " sales records");
        $this->command->info("📊 Sales data includes:");
        $this->command->info("   • Product types: Rings, Necklaces, Bracelets, Earrings, Chains, Pendants");
        $this->command->info("   • Price range: RM 1,800 - RM 5,500");
        $this->command->info("   • Assigned to all active agent users");

        // Display sample data
        $this->command->info("\n📋 Sample sales records:");
        foreach ($salesData as $index => $data) {
            $this->command->info("   {$data['variety_type']} - RM " . number_format($data['total_price'], 2));
        }
    }
}
