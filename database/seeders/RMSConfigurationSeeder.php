<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;

class RMSConfigurationSeeder extends Seeder
{
    protected static $table = 'rms_gold_commerce_settings';

    /**
     * Run the database seeds
     * .
     */
    public function run(): void
    {
        DB::table(self::$table)->truncate();
        DB::table(self::$table)->insert([
            'api_key' => 'Sankyu123',
            'api_secret' => 'Sankyu123',
            'api_url' => 'http://api.hos0sgpzxv-wg96gkodm4oy.p.temp-site.link/',
            'is_active' => true
        ]);
    }
}
