<?php

namespace App\Livewire\Admin;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Livewire\Component;
use App\Models\AcsSalesCommission;
use App\Models\AcsCommissionDistribution;

class CommissionGeneration extends Component
{
    public $lastCalculateDate = '';
    public $previewTotalMainAgent = 0;
    public $previewTotalAgent = 0;
    public $cooperativeBreakdown = [];

    // Checkbox functionality for selective processing
    public $selectedCooperatives = [];
    public $selectAll = false;
    public $useDefaultCommission = false;

    public function mount()
    {
        // Set default last calculate date to today
        $this->lastCalculateDate = now()->format('Y-m-d');

        // Calculate initial preview totals
        $this->calculatePreviewTotals();
    }

    public function render()
    {
        return view('livewire.admin.commission-generation');
    }

    public function calculatePreviewTotals()
    {
        // Reset values
        $this->previewTotalMainAgent = 0;
        $this->previewTotalAgent = 0;
        $this->cooperativeBreakdown = [];

        if (!$this->lastCalculateDate) {
            return;
        }

        // Get aggregated sales data grouped by user (only status = 0 and up to selected date)
        $userSales = DB::table('acs_sales')
                ->join('acs_users', 'acs_sales.acs_agent_user_id', '=', 'acs_users.id')
                ->leftJoin('acs_comission_settings', 'acs_sales.variety_type', '=', 'acs_comission_settings.variety_type')
                ->leftJoin('acs_coorperative', 'acs_users.acs_coorperative_id', '=', 'acs_coorperative.id')
                ->where('acs_sales.status', 0) // Only unprocessed sales
                ->whereDate('acs_sales.created_at', '<=', $this->lastCalculateDate) // Only up to selected date
                ->select(
                    'acs_sales.acs_agent_user_id',
                    'acs_users.name as user_name',
                    'acs_users.email as user_email',
                    'acs_users.acs_coorperative_id',
                    'acs_coorperative.name as cooperative_name',
                    DB::raw('COUNT(*) as quantity'),
                    DB::raw('SUM(acs_sales.total_price) as total_price'),
                    'acs_sales.variety_type',
                    'acs_comission_settings.variety_name'
                )
                ->groupBy('acs_sales.acs_agent_user_id', 'acs_users.name', 'acs_users.email', 'acs_users.acs_coorperative_id', 'acs_coorperative.name', 'acs_sales.variety_type', 'acs_comission_settings.variety_name')
                ->get();

        // If no unprocessed sales found for the selected date range, return early
        if ($userSales->isEmpty()) {
            return;
        }

        $totalMainAgentPayout = 0;
        $totalAgentPayout = 0;
        $cooperativeBreakdown = [];

        foreach($userSales as $userSale){
            // Find the commission tier for this user's total quantity
            $tierCommission = DB::table('acs_commission_tiers')
                    ->join('acs_comission_settings', 'acs_commission_tiers.commission_setting_id', '=', 'acs_comission_settings.id')
                    ->where('acs_comission_settings.variety_type', $userSale->variety_type)
                    ->where('acs_commission_tiers.min_qty', '<=', $userSale->quantity)
                    ->where('acs_commission_tiers.max_qty', '>=', $userSale->quantity)
                    ->whereNull('acs_commission_tiers.deleted_at')
                    ->select(
                        'acs_commission_tiers.commission_percentage',
                        'acs_commission_tiers.min_qty',
                        'acs_commission_tiers.max_qty',
                        'acs_comission_settings.variety_type'
                    )
                    ->first();

            // Calculate commission amount
            $commissionAmount = 0;
            $commissionPercentage = 0;

            if ($tierCommission) {
                $commissionPercentage = $tierCommission->commission_percentage;
                $commissionAmount = ($userSale->total_price * $commissionPercentage) / 100;
            }

            // Get commission split settings
            if ($this->useDefaultCommission) {
                // Use default settings when checkbox is checked
                $commissionSplitSettings = DB::table('acs_comission_split_settings')
                    ->where('default', true)
                    ->whereNull('deleted_at')
                    ->first();
            } else {
                // Use normal logic: cooperative-specific first, then default
                $commissionSplitSettings = DB::table('acs_comission_split_settings')
                    ->where('acs_coorperative_id', $userSale->acs_coorperative_id)
                    ->whereNull('deleted_at')
                    ->first();

                // If no cooperative-specific settings found, fallback to default settings
                if (!$commissionSplitSettings) {
                    $commissionSplitSettings = DB::table('acs_comission_split_settings')
                        ->where('default', true)
                        ->whereNull('deleted_at')
                        ->first();
                }
            }

            // If still no settings found, log error and skip this record
            if (!$commissionSplitSettings) {
                Log::warning('No commission split settings found for cooperative ID: ' . $userSale->acs_coorperative_id);
                continue;
            }

            $mainAgentPercentage = $commissionSplitSettings->main_agent_percentage;
            $agentPercentage = $commissionSplitSettings->agent_percentage;

            $agentCommission = number_format($agentPercentage / 100 * $commissionAmount, 2, '.', '');
            $masterAgentCommission = number_format($mainAgentPercentage / 100 * $commissionAmount, 2, '.', '');

            // Accumulate by cooperative
            $cooperativeId = $userSale->acs_coorperative_id;
            $cooperativeName = $userSale->cooperative_name ?? 'Coop ID: ' . $cooperativeId;

            if (!isset($cooperativeBreakdown[$cooperativeId])) {
                $cooperativeBreakdown[$cooperativeId] = [
                    'id' => $cooperativeId,
                    'name' => $cooperativeName,
                    'main_agent_commission' => 0,
                    'agent_commission' => 0,
                    'total_commission' => 0,
                    'sales_count' => 0,
                    'sales_value' => 0,
                    'main_agent_percentage' => $mainAgentPercentage,
                    'agent_percentage' => $agentPercentage
                ];
            }

            $cooperativeBreakdown[$cooperativeId]['main_agent_commission'] += floatval($masterAgentCommission);
            $cooperativeBreakdown[$cooperativeId]['agent_commission'] += floatval($agentCommission);
            $cooperativeBreakdown[$cooperativeId]['total_commission'] += floatval($masterAgentCommission) + floatval($agentCommission);
            $cooperativeBreakdown[$cooperativeId]['sales_count'] += $userSale->quantity;
            $cooperativeBreakdown[$cooperativeId]['sales_value'] += $userSale->total_price;

            // Accumulate payout totals
            $totalMainAgentPayout += floatval($masterAgentCommission);
            $totalAgentPayout += floatval($agentCommission);
        }

        $this->previewTotalMainAgent = $totalMainAgentPayout;
        $this->previewTotalAgent = $totalAgentPayout;
        $this->cooperativeBreakdown = array_values($cooperativeBreakdown); // Convert to indexed array for easier iteration

        // Initialize checkbox states for new cooperatives
        foreach ($this->cooperativeBreakdown as $cooperative) {
            $cooperativeId = $cooperative['id'];
            if (!isset($this->selectedCooperatives[$cooperativeId])) {
                $this->selectedCooperatives[$cooperativeId] = false;
            }
        }
    }

    // Checkbox related methods
    public function toggleSelectAll()
    {
        foreach ($this->cooperativeBreakdown as $cooperative) {
            $this->selectedCooperatives[$cooperative['id']] = $this->selectAll;
        }
    }

    public function updatedUseDefaultCommission()
    {
        // This method is automatically called when useDefaultCommission property changes
        $this->calculatePreviewTotals();

        // Flash message to confirm the checkbox is working
        if ($this->useDefaultCommission) {
            session()->flash('success', 'Now using default commission percentages for all cooperatives.');
        } else {
            session()->flash('success', 'Now using individual cooperative commission settings.');
        }
    }

    public function toggleCooperativeSelection($cooperativeId)
    {
        $this->selectedCooperatives[$cooperativeId] = !($this->selectedCooperatives[$cooperativeId] ?? false);

        // Update select all checkbox state
        $totalCooperatives = count($this->cooperativeBreakdown);
        $selectedCount = count(array_filter($this->selectedCooperatives));
        $this->selectAll = $selectedCount === $totalCooperatives;
    }

    public function processSelectedCooperatives()
    {
        // Validate date
        if (!$this->lastCalculateDate) {
            session()->flash('error', 'Please select a last calculate date.');
            return;
        }

        // Check if any cooperatives are selected
        $selectedCoopIds = array_keys(array_filter($this->selectedCooperatives));
        if (empty($selectedCoopIds)) {
            session()->flash('error', 'Please select at least one cooperative to process.');
            return;
        }

        $this->processGenerationForCooperatives($selectedCoopIds);
    }

    public function processGeneration()
    {
        // Validate date
        if (!$this->lastCalculateDate) {
            session()->flash('error', 'Please select a last calculate date.');
            return;
        }

        // Process all cooperatives
        $allCoopIds = array_column($this->cooperativeBreakdown, 'id');
        $this->processGenerationForCooperatives($allCoopIds);
    }

    private function processGenerationForCooperatives($cooperativeIds)
    {
        // Get aggregated sales data grouped by user (only status = 0 and up to selected date)
        $userSales = DB::table('acs_sales')
                ->join('acs_users', 'acs_sales.acs_agent_user_id', '=', 'acs_users.id')
                ->leftJoin('acs_comission_settings', 'acs_sales.variety_type', '=', 'acs_comission_settings.variety_type')
                ->leftJoin('acs_coorperative', 'acs_users.acs_coorperative_id', '=', 'acs_coorperative.id')
                ->where('acs_sales.status', 0) // Only unprocessed sales
                ->whereDate('acs_sales.created_at', '<=', $this->lastCalculateDate) // Only up to selected date
                ->select(
                    'acs_sales.acs_agent_user_id',
                    'acs_users.name as user_name',
                    'acs_users.email as user_email',
                    'acs_users.acs_coorperative_id',
                    'acs_coorperative.name as cooperative_name',
                    DB::raw('COUNT(*) as quantity'),
                    DB::raw('SUM(acs_sales.total_price) as total_price'),
                    'acs_sales.variety_type',
                    'acs_comission_settings.variety_name'
                )
                ->groupBy('acs_sales.acs_agent_user_id', 'acs_users.name', 'acs_users.email', 'acs_users.acs_coorperative_id', 'acs_coorperative.name', 'acs_sales.variety_type', 'acs_comission_settings.variety_name')
                ->get();

        if ($userSales->isEmpty()) {
            session()->flash('error', "No unprocessed sales found for the selected cooperatives on date {$this->lastCalculateDate}.");
            return;
        }

        $totalMainAgentPayout = 0;
        $totalAgentPayout = 0;

        foreach($userSales as $userSale){
            // Find the commission tier for this user's total quantity
            $tierCommission = DB::table('acs_commission_tiers')
                    ->join('acs_comission_settings', 'acs_commission_tiers.commission_setting_id', '=', 'acs_comission_settings.id')
                    ->where('acs_comission_settings.variety_type', $userSale->variety_type)
                    ->where('acs_commission_tiers.min_qty', '<=', $userSale->quantity)
                    ->where('acs_commission_tiers.max_qty', '>=', $userSale->quantity)
                    ->whereNull('acs_commission_tiers.deleted_at')
                    ->select(
                        'acs_commission_tiers.commission_percentage',
                        'acs_commission_tiers.min_qty',
                        'acs_commission_tiers.max_qty',
                        'acs_comission_settings.variety_type'
                    )
                    ->first();

            // Calculate commission amount
            $commissionAmount = 0;
            $commissionPercentage = 0;

            if ($tierCommission) {
                $commissionPercentage = $tierCommission->commission_percentage;
                $commissionAmount = ($userSale->total_price * $commissionPercentage) / 100;
            }

            // Get commission split settings specific to the cooperative first
            $commissionSplitSettings = DB::table('acs_comission_split_settings')
                ->where('acs_coorperative_id', $userSale->acs_coorperative_id)
                ->whereNull('deleted_at')
                ->first();

            // If no cooperative-specific settings found, fallback to default settings
            if (!$commissionSplitSettings) {
                $commissionSplitSettings = DB::table('acs_comission_split_settings')
                    ->where('default', true)
                    ->whereNull('deleted_at')
                    ->first();
            }

            // If still no settings found, log error and skip this record
            if (!$commissionSplitSettings) {
                Log::warning('No commission split settings found for cooperative ID: ' . $userSale->acs_coorperative_id);
                continue;
            }

            $mainAgentPercentage = $commissionSplitSettings->main_agent_percentage;
            $agentPercentage = $commissionSplitSettings->agent_percentage;

            $agentCommission = number_format($agentPercentage / 100 * $commissionAmount, 2, '.', '');
            $masterAgentCommission = number_format($mainAgentPercentage / 100 * $commissionAmount, 2, '.', '');

            // Save to database if commission found
            if ($tierCommission && $commissionAmount > 0) {
                DB::beginTransaction();
                try {

                    // Get user details for commission distribution
                    $userDetails = DB::table('acs_users')
                        ->where('id', $userSale->acs_agent_user_id)
                        ->first();

                    // Get all sales that match the grouped criteria
                    $salesToProcess = DB::table('acs_sales')
                        ->where('acs_agent_user_id', $userSale->acs_agent_user_id)
                        ->where('variety_type', $userSale->variety_type)
                        ->where('status', 0)
                        ->whereDate('created_at', '<=', $this->lastCalculateDate)
                        ->get();

                    // Calculate commission per sale
                    $commissionPerSale = $commissionAmount / $userSale->quantity;

                    foreach ($salesToProcess as $sale) {
                        // Create commission distribution record for each sale
                        AcsCommissionDistribution::create([
                            'acs_sales_id' => $sale->id,
                            'acs_coorperative_id' => $userSale->acs_coorperative_id,
                            'acs_coorperative_branch_id' => $userDetails->acs_coorperative_branch_id ?? 1,
                            'acs_main_agent_user_id' => $userDetails->invited_by ?? $userSale->acs_agent_user_id,
                            'main_agent_percentage' => $mainAgentPercentage,
                            'main_agent_commission_amount' => ($masterAgentCommission / $userSale->quantity),
                            'agent_percentage' => $agentPercentage,
                            'agent_commission_amount' => ($agentCommission / $userSale->quantity),
                            'acs_agent_user_id' => $userSale->acs_agent_user_id,
                        ]);
                    }

                    DB::commit();
                } catch (\Exception $e) {
                    DB::rollback();
                    // Log error for debugging
                    Log::error('Commission distribution save failed: ' . $e->getMessage());
                }
            }

            // Accumulate payout totals
            $totalMainAgentPayout += floatval($masterAgentCommission);
            $totalAgentPayout += floatval($agentCommission);
        }

        // Update sales status to 1 only for sales up to the selected date and selected cooperatives
        $updatedCount = DB::table('acs_sales')
            ->join('acs_users', 'acs_sales.acs_agent_user_id', '=', 'acs_users.id')
            ->where('acs_sales.status', 0)
            ->whereDate('acs_sales.created_at', '<=', $this->lastCalculateDate)
            ->whereIn('acs_users.acs_coorperative_id', $cooperativeIds)
            ->update(['acs_sales.status' => 1]);

        // Check if there are still unprocessed sales remaining
        $remainingUnprocessedSales = DB::table('acs_sales')->where('status', 0)->count();

        $processedCooperatives = implode(', ', array_map(function($id) {
            $coop = collect($this->cooperativeBreakdown)->firstWhere('id', $id);
            return $coop ? $coop['name'] : "Coop ID: $id";
        }, $cooperativeIds));

        session()->flash('success', "Commission report generated successfully! Processed {$updatedCount} sales for cooperatives: {$processedCooperatives} up to {$this->lastCalculateDate}. " .
                         ($remainingUnprocessedSales > 0 ? "{$remainingUnprocessedSales} unprocessed sales remain." : "All sales have been processed."));

        // Refresh the data after processing
        $this->calculatePreviewTotals();
    }

    public function updatedLastCalculateDate()
    {
        $this->calculatePreviewTotals();
    }

    public function getSalesStatsProperty()
    {
        $totalSales = DB::table('acs_sales')->count();
        $processedSales = DB::table('acs_sales')->where('status', 1)->count();
        $unprocessedSales = DB::table('acs_sales')->where('status', 0)->count();
        $totalValue = DB::table('acs_sales')->sum('total_price');
        $unprocessedValue = DB::table('acs_sales')->where('status', 0)->sum('total_price');

        return [
            'total_sales' => $totalSales,
            'processed_sales' => $processedSales,
            'unprocessed_sales' => $unprocessedSales,
            'total_value' => $totalValue,
            'unprocessed_value' => $unprocessedValue,
        ];
    }
}
