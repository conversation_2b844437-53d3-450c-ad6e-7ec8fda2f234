<?php

namespace App\Livewire\Admin;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Livewire\Component;
use Livewire\WithPagination;
use App\Models\AcsSalesCommission;
use App\Models\AcsCommissionDistribution;

class GenerateReport extends Component
{
    use WithPagination;

    public $isSubmitted = false;
    public $activeTab = 'sales'; // 'sales' or 'history'
    public $searchTerm = '';
    public $dateTo = '';
    public $processedDateTo = ''; // For filtering processed sales by date



    protected $paginationTheme = 'bootstrap';

    public function mount()
    {
        // Check if there are any unprocessed sales (status = 0)
        // If no unprocessed sales exist, consider it submitted
        $this->isSubmitted = !DB::table('acs_sales')->where('status', 0)->exists();
    }

    public function render()
    {
        return view('livewire.admin.generate-report');
    }









    public function refreshStatus()
    {
        // Refresh the submission status by checking for unprocessed sales
        $this->isSubmitted = !DB::table('acs_sales')->where('status', 0)->exists();

        // Reset any cached data
        $this->resetPage();

        session()->flash('info', 'Status refreshed. ' .
                         ($this->isSubmitted ? 'No unprocessed sales found.' : 'Unprocessed sales are available for processing.'));
    }



        public function setActiveTab($tab)
    {
        $this->activeTab = $tab;
        $this->resetPage();
    }

    public function updatedSearchTerm()
    {
        $this->resetPage();
    }



    public function updatedDateTo()
    {
        $this->resetPage();
    }

    public function updatedProcessedDateTo()
    {
        $this->resetPage();
    }

    public function getCommissionHistoryProperty()
    {
        $query = DB::table('acs_sales')
            ->join('acs_users', 'acs_sales.acs_agent_user_id', '=', 'acs_users.id')
            ->leftJoin('acs_comission_settings', 'acs_sales.variety_type', '=', 'acs_comission_settings.variety_type')
            ->leftJoin('acs_coorperative', 'acs_users.acs_coorperative_id', '=', 'acs_coorperative.id')
            ->where('acs_sales.status', 1) // Only processed sales
            ->select(
                'acs_sales.acs_agent_user_id',
                'acs_users.name as user_name',
                'acs_users.email as user_email',
                'acs_users.acs_coorperative_id',
                'acs_coorperative.name as cooperative_name',
                'acs_sales.variety_type',
                'acs_comission_settings.variety_name',
                DB::raw('COUNT(*) as quantity'),
                DB::raw('SUM(acs_sales.total_price) as total_price'),
                DB::raw('GROUP_CONCAT(DISTINCT acs_sales.sku_number SEPARATOR ", ") as sku_numbers'),
                DB::raw('MIN(acs_sales.created_at) as first_sale_date'),
                DB::raw('MAX(acs_sales.created_at) as last_sale_date'),
                DB::raw('MAX(acs_sales.updated_at) as processed_date')
            )
            ->groupBy(
                'acs_sales.acs_agent_user_id',
                'acs_users.name',
                'acs_users.email',
                'acs_users.acs_coorperative_id',
                'acs_coorperative.name',
                'acs_sales.variety_type',
                'acs_comission_settings.variety_name'
            )
            ->orderBy('last_sale_date', 'desc');

        // Apply search filter
        if ($this->searchTerm) {
            $query->where(function($q) {
                $q->where('acs_users.name', 'like', '%' . $this->searchTerm . '%')
                  ->orWhere('acs_users.email', 'like', '%' . $this->searchTerm . '%')
                  ->orWhere('acs_sales.variety_type', 'like', '%' . $this->searchTerm . '%')
                  ->orWhere('acs_comission_settings.variety_name', 'like', '%' . $this->searchTerm . '%');
            });
        }

        // Apply date filter (created date)
        if ($this->dateTo) {
            $query->whereDate('acs_sales.created_at', '<=', $this->dateTo);
        }

        // Apply processed date filter
        if ($this->processedDateTo) {
            $query->whereDate('acs_sales.updated_at', '<=', $this->processedDateTo);
        }

        return $query->paginate(15);
    }

    public function getHistoryStatsProperty()
    {
        // Base query for processed sales
        $processedSalesQuery = DB::table('acs_sales')->where('status', 1);
        $totalValueQuery = DB::table('acs_sales')->where('status', 1);

        // Apply date filters if set
        if ($this->dateTo) {
            $processedSalesQuery->whereDate('created_at', '<=', $this->dateTo);
            $totalValueQuery->whereDate('created_at', '<=', $this->dateTo);
        }

        if ($this->processedDateTo) {
            $processedSalesQuery->whereDate('updated_at', '<=', $this->processedDateTo);
            $totalValueQuery->whereDate('updated_at', '<=', $this->processedDateTo);
        }

        $processedSales = $processedSalesQuery->count();
        $totalValue = $totalValueQuery->sum('total_price');

        // Get commission data from sales commission table
        $commissionStats = AcsSalesCommission::selectRaw('
            COUNT(*) as total_records,
            SUM(comission_amount) as total_commission,
            AVG(comission_amount) as avg_commission,
            MAX(comission_amount) as max_commission
        ')->first();

        $distributionStats = AcsCommissionDistribution::selectRaw('
            SUM(main_agent_commission_amount) as total_main_agent,
            SUM(agent_commission_amount) as total_agent
        ')->first();

        return [
            'total_records' => $commissionStats->total_records ?? 0,
            'total_commission' => $commissionStats->total_commission ?? 0,
            'avg_commission' => $commissionStats->avg_commission ?? 0,
            'max_commission' => $commissionStats->max_commission ?? 0,
            'total_main_agent' => $distributionStats->total_main_agent ?? 0,
            'total_agent' => $distributionStats->total_agent ?? 0,
            'processed_sales' => $processedSales,
            'total_value' => $totalValue,
        ];
    }

    public function getSalesListProperty()
    {
        $query = DB::table('acs_sales')
            ->join('acs_users', 'acs_sales.acs_agent_user_id', '=', 'acs_users.id')
            ->leftJoin('acs_comission_settings', 'acs_sales.variety_type', '=', 'acs_comission_settings.variety_type')
            ->leftJoin('acs_coorperative', 'acs_users.acs_coorperative_id', '=', 'acs_coorperative.id')
            ->where('acs_sales.status', 0) // Only unprocessed sales
            ->select(
                'acs_sales.acs_agent_user_id',
                'acs_users.name as user_name',
                'acs_users.email as user_email',
                'acs_users.acs_coorperative_id',
                'acs_coorperative.name as cooperative_name',
                'acs_sales.variety_type',
                'acs_comission_settings.variety_name',
                DB::raw('COUNT(*) as quantity'),
                DB::raw('SUM(acs_sales.total_price) as total_price'),
                DB::raw('GROUP_CONCAT(DISTINCT acs_sales.sku_number SEPARATOR ", ") as sku_numbers'),
                DB::raw('MIN(acs_sales.created_at) as first_sale_date'),
                DB::raw('MAX(acs_sales.created_at) as last_sale_date')
            )
            ->groupBy(
                'acs_sales.acs_agent_user_id',
                'acs_users.name',
                'acs_users.email',
                'acs_users.acs_coorperative_id',
                'acs_coorperative.name',
                'acs_sales.variety_type',
                'acs_comission_settings.variety_name'
            )
            ->orderBy('last_sale_date', 'desc');

        // Apply search filter
        if ($this->searchTerm) {
            $query->where(function($q) {
                $q->where('acs_users.name', 'like', '%' . $this->searchTerm . '%')
                  ->orWhere('acs_users.email', 'like', '%' . $this->searchTerm . '%')
                  ->orWhere('acs_sales.variety_type', 'like', '%' . $this->searchTerm . '%')
                  ->orWhere('acs_comission_settings.variety_name', 'like', '%' . $this->searchTerm . '%');
            });
        }

        // Apply date filter
        if ($this->dateTo) {
            $query->whereDate('acs_sales.created_at', '<=', $this->dateTo);
        }

        return $query->paginate(15);
    }

    public function getSalesStatsProperty()
    {
        $totalSales = DB::table('acs_sales')->count();
        $processedSales = DB::table('acs_sales')->where('status', 1)->count();
        $unprocessedSales = DB::table('acs_sales')->where('status', 0)->count();
        $totalValue = DB::table('acs_sales')->sum('total_price');
        $unprocessedValue = DB::table('acs_sales')->where('status', 0)->sum('total_price');

        return [
            'total_sales' => $totalSales,
            'processed_sales' => $processedSales,
            'unprocessed_sales' => $unprocessedSales,
            'total_value' => $totalValue,
            'unprocessed_value' => $unprocessedValue,
        ];
    }

    public function calculateSaleCommission($sale)
    {
        // Use the quantity from the grouped data (already calculated)
        $quantity = $sale->quantity;

        // Find the commission tier
        $tierCommission = DB::table('acs_commission_tiers')
            ->join('acs_comission_settings', 'acs_commission_tiers.commission_setting_id', '=', 'acs_comission_settings.id')
            ->where('acs_comission_settings.variety_type', $sale->variety_type)
            ->where('acs_commission_tiers.min_qty', '<=', $quantity)
            ->where('acs_commission_tiers.max_qty', '>=', $quantity)
            ->where('acs_commission_tiers.deleted_at', null)
            ->select('acs_commission_tiers.commission_percentage')
            ->first();

        $commissionPercentage = $tierCommission ? $tierCommission->commission_percentage : 0;
        $commissionAmount = ($sale->total_price * $commissionPercentage) / 100;

        // Get commission split settings specific to the cooperative first
        $commissionSplitSettings = DB::table('acs_comission_split_settings')
            ->where('acs_coorperative_id', $sale->acs_coorperative_id)
            ->whereNull('deleted_at')
            ->first();

        // If no cooperative-specific settings found, fallback to default settings
        if (!$commissionSplitSettings) {
            $commissionSplitSettings = DB::table('acs_comission_split_settings')
                ->where('default', true)
                ->whereNull('deleted_at')
                ->first();
        }

        // If still no settings found, log error and return zero commissions
        if (!$commissionSplitSettings) {
            Log::warning('No commission split settings found for cooperative ID: ' . $sale->acs_coorperative_id);
            return [
                'quantity_for_tier' => $quantity,
                'commission_percentage' => $commissionPercentage,
                'commission_amount' => $commissionAmount,
                'agent_commission' => 0,
                'master_agent_commission' => 0,
                'main_agent_percentage' => 0,
                'agent_percentage' => 0,
                'tier_found' => $tierCommission ? true : false,
                'split_settings_found' => false
            ];
        }

        $mainAgentPercentage = $commissionSplitSettings->main_agent_percentage;
        $agentPercentage = $commissionSplitSettings->agent_percentage;

        $agentCommission = ($commissionAmount * $agentPercentage) / 100;
        $masterAgentCommission = ($commissionAmount * $mainAgentPercentage) / 100;

        return [
            'quantity_for_tier' => $quantity,
            'commission_percentage' => $commissionPercentage,
            'commission_amount' => $commissionAmount,
            'agent_commission' => $agentCommission,
            'master_agent_commission' => $masterAgentCommission,
            'main_agent_percentage' => $mainAgentPercentage,
            'agent_percentage' => $agentPercentage,
            'tier_found' => $tierCommission ? true : false,
            'split_settings_found' => true
        ];
    }


}
