<?php

namespace App\Jobs;

use App\Models\WebhookEvent;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;

class StoreWebhookEventJob implements ShouldQueue
{
    use Queueable;

    public $payload = [];

    public string $requestId;

    public string $ipaddress;

    public $queueName = 'StoreWebhookEventJob';

    /**
     * Create a new job instance.
     */
    public function __construct($payload = [], ?string $requestId, ?string $ipaddress)
    {
        $this->payload = $payload;
        $this->requestId = $requestId;
        $this->ipaddress = $ipaddress;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        logger()->info("Capturing webhook event : {$this->requestId}");

        WebhookEvent::create([
            'event_name' => 'sales.completed',
            'ip_address' => $this->ipaddress,
            'request_id' => $this->requestId,
            'payload' => $this->payload,
            'received_at' => now()->toDateTimeString()
        ]);
    }
}
