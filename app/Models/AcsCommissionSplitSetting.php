<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class AcsCommissionSplitSetting extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'acs_comission_split_settings';

    protected $fillable = [
        'acs_coorperative_id',
        'acs_agent_user_id',
        'main_agent_percentage',
        'agent_percentage',
        'default',
    ];

    protected $casts = [
        'acs_coorperative_id' => 'integer',
        'main_agent_percentage' => 'decimal:2',
        'agent_percentage' => 'decimal:2',
        'default' => 'boolean',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    /**
     * Get the cooperative that owns the commission split setting
     */
    public function cooperative(): BelongsTo
    {
        return $this->belongsTo(AcsCooperative::class, 'acs_coorperative_id');
    }

    /**
     * Scope to get default settings
     */
    public function scopeDefault($query)
    {
        return $query->where('default', true);
    }

    /**
     * Scope to get settings for a specific cooperative
     */
    public function scopeForCooperative($query, $cooperativeId)
    {
        return $query->where('acs_coorperative_id', $cooperativeId);
    }
}
