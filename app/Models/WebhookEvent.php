<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Contracts\Database\Eloquent\Builder;

class WebhookEvent extends Model
{
    protected $guarded = ['id'];

    protected $casts = ['completed' => 'boolean'];

    public function markAsCompleted(): bool
    {
        return $this->forceFill(['completed' => true])->save();
    }

    public function scopePendingWithRequestId(Builder $query, $requestId)
    {
        return $this->query->where('request_id', $requestId);
    }
}
