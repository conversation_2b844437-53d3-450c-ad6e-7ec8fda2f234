<?php

namespace App\Http\Controllers\Api;

use Exception;
use App\Models\User;
use App\Models\AcsRole;
use Illuminate\Support\Str;
use Illuminate\Http\Request;
use App\Jobs\StoreWebhookEventJob;
use App\Models\AcsSalesCommission;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\Controller;
use App\Models\AcsCommissionSetting;
use Illuminate\Support\Facades\Http;
use App\Models\AcsCommissionDistribution;

class SalesCommissionSubmissionController extends Controller
{
    /**
     * Handle the incoming request.
     */
    public function __invoke(Request $request)
    {
        Log::info('commission_requests', $request->all());
        $callbackReq = Http::get('http://api.hos0sgpzxv-wg96gkodm4oy.p.temp-site.link/b0620426714044bebe9ff7b1b7370/fake/callback');
        $responseData = json_decode($callbackReq, true);
        $customerId = $responseData['data']['order']['customer_id'] ?? null;
        $agentReferralCode = $responseData['data']['order']['agent_referral_code'] ?? null;
        $productData = $responseData['data']['product'] ?? [];
        $requestSignature = hash('sha256', implode('|', $request->all()));
        $getAgentByReferralCode = User::where('referral_code', $agentReferralCode)->first();
        $products = [];
        $timestamp = now();

        if ($getAgentByReferralCode->role?->name != AcsRole::AGENT_ROLE) {
            return response()->json([
                'error' => false,
                'status_code' => 400,
                'data' => 'Current user is not agent, or agent is not found.'
            ]);
        }

        if (is_null($customerId) || is_null($agentReferralCode)) {
            return response()->json([
                'error' => true,
                'status' => 400,
                'data' => 'Missing customer ID or agent referral code'
            ]);
        }

        foreach ($productData as $product) {
            $products[] = [
                'id' => Str::ulid(),
                'acs_agent_user_id' => $getAgentByReferralCode->id,
                'acs_coorperative_id' => $getAgentByReferralCode->acs_coorperative_id,
                'acs_coorperative_branch_id' => $getAgentByReferralCode->acs_coorperative_branch_id,
                'cents' => $product['price_cents'],
                'total_price' => $product['price'],
                'purity' => $product['purity'],
                'variety_type' => $product['variety_id'],
                '23_senarai_jualan_id' => $product['23_senarai_jualan_id'],
                'sku_number' => $product['sku_number'],
                'design_code' => $product['design_code'],
                'design_name' => $product['design_name'],
                'description' => $product['description'],
                'created_at' => $timestamp,
                'updated_at' => $timestamp,
            ];
        }
        //before attempt to save push into queue using redis driver to continue without interruption response to client callback
        //mocking request ID
        StoreWebhookEventJob::dispatch(json_encode($responseData), Str::random(20), $request->ip());

        try {
            //attempt 2 times
            DB::transaction(function () use ($products) {
                DB::table('acs_sales')->insert($products);
            }, 2);

        } catch (Exception $e) {
            Log::error('Failed to insert products: ' . $e->getMessage());
            return response()->json([
                'status' => 500,
                'error' => true,
                'data' => 'Failed to record sales',
                'details' => $e->getMessage()
            ]);
        }

        return response()->json([
            'status' => 200,
            'error' => false,
            'data' => $products
        ]);
    }
}
