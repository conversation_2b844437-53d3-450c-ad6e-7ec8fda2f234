<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Session\TokenMismatchException;
use Symfony\Component\HttpFoundation\Response;

class HandleSessionExpired
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     * @return \Symfony\Component\HttpFoundation\Response
     */
    public function handle(Request $request, Closure $next): Response
    {
        try {
            return $next($request);
        } catch (TokenMismatchException $e) {
            // Handle CSRF token mismatch which often indicates session expiry
            return $this->handleExpiredSession($request);
        }
    }

    /**
     * Handle expired session by redirecting to custom page
     */
    protected function handleExpiredSession(Request $request): Response
    {
        // If it's an AJAX/Livewire request, return JSON response
        if ($request->expectsJson() || $request->header('X-Livewire')) {
            return response()->json([
                'message' => 'Session expired. Please refresh the page and login again.',
                'redirect' => route('session.expired')
            ], 419);
        }

        // For regular requests, redirect to session expired page
        return redirect()->route('session.expired');
    }
}
