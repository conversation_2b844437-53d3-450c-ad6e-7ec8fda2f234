<script src="{{asset('assets/js/plugins/popper.min.js')}}"></script>
<script src="{{asset('assets/js/plugins/simplebar.min.js')}}"></script>
<script src="{{asset('assets/js/plugins/bootstrap.min.js')}}"></script>
<script src="{{asset('assets/js/plugins/i18next.min.js')}}"></script>
<script src="{{asset('assets/js/plugins/i18nextHttpBackend.min.js')}}"></script>
<script src="{{asset('assets/js/script.js')}}"></script>
<script src="{{asset('assets/js/theme.js')}}"></script>
<script src="{{asset('assets/js/multi-lang.js')}}"></script>
<script src="{{asset('assets/js/plugins/feather.min.js')}}"></script>
<script>layout_change('light');</script>
<script>layout_sidebar_change('dark');</script>
<script>change_box_container('false');</script>
<script>layout_caption_change('true');</script>
<script>layout_rtl_change('false');</script>
<script>preset_change('preset-1');</script>

@stack('scripts')
<script>
   // Copy to clipboard functionality for Livewire
   window.addEventListener('copy-to-clipboard', event => {
      if (navigator.clipboard) {
         navigator.clipboard.writeText(event.detail).then(function() {
            // Simple alert for success
            alert('Link copied to clipboard!');
         }).catch(function() {
            // Fallback for older browsers
            alert('Link copied: ' + event.detail);
         });
      } else {
         // Fallback for older browsers
         alert('Link: ' + event.detail);
      }
   });

   // Handle redirect to login for password creation
   window.addEventListener('redirect-to-login', event => {
      setTimeout(() => {
         window.location.href = '/login';
      }, 3000);
   });

   // Global AJAX error handler for session expiry
   $(document).ajaxError(function(event, xhr, settings) {
      if (xhr.status === 419) {
         // Session expired - redirect to custom page
         window.location.href = "{{ route('session.expired') }}";
      }
   });

   // Livewire global error handler
   document.addEventListener('livewire:init', () => {
      Livewire.hook('request', ({ fail }) => {
         fail(({ status, content, preventDefault }) => {
            if (status === 419) {
               preventDefault();
               window.location.href = "{{ route('session.expired') }}";
            }
         });
      });
   });

   // Handle fetch API requests
   const originalFetch = window.fetch;
   window.fetch = function(...args) {
      return originalFetch.apply(this, args).then(response => {
         if (response.status === 419) {
            window.location.href = "{{ route('session.expired') }}";
         }
         return response;
      });
   };

</script>
<!-- [Livewire Scripts] -->
@livewireScripts




