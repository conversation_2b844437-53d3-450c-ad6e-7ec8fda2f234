<div>
    {{-- <PERSON>er --}}
    <div class="page-header">
        <div class="page-block">
            <div class="row align-items-center">
                <div class="col-md-12">
                    <div class="d-flex justify-content-between align-items-center">
                        <div class="page-header-title">
                            <h2 class="mb-0">Generate Commission Report</h2>
                            <p class="text-muted mb-0 mt-2">Process unprocessed sales and generate commission distributions</p>
                        </div>
                        <div>
                            <a href="{{ route('admin.generate-report') }}" class="btn btn-outline-secondary">
                                <i class="ph-duotone ph-arrow-left me-2"></i>Back to Sales Management
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    {{-- Flash Messages --}}
    @if (session()->has('success'))
        <div class="alert alert-success alert-dismissible fade show border-0" role="alert" style="border-radius: 12px;">
            <div class="d-flex align-items-start">
                <div class="flex-shrink-0 me-3">
                    <div class="avtar avtar-s bg-success" style="border-radius: 8px;">
                        <i class="ph-duotone ph-check-circle"></i>
                    </div>
                </div>
                <div class="flex-grow-1">
                    <div class="fw-semibold text-success mb-1">Success!</div>
                    <div class="text-dark">{{ session('success') }}</div>
                </div>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        </div>
    @endif

    @if (session()->has('error'))
        <div class="alert alert-danger alert-dismissible fade show border-0" role="alert" style="border-radius: 12px;">
            <div class="d-flex align-items-start">
                <div class="flex-shrink-0 me-3">
                    <div class="avtar avtar-s bg-danger" style="border-radius: 8px;">
                        <i class="ph-duotone ph-x-circle"></i>
                    </div>
                </div>
                <div class="flex-grow-1">
                    <div class="fw-semibold text-danger mb-1">Error!</div>
                    <div class="text-dark">{{ session('error') }}</div>
                </div>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        </div>
    @endif

    {{-- Statistics Cards --}}
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="avtar avtar-s bg-light-warning">
                            <i class="ph-duotone ph-clock"></i>
                        </div>
                        <div class="flex-grow-1">
                            <p class="mb-1 text-muted">Ready to Process</p>
                            <h4 class="mb-0 fw-bold">{{ $this->salesStats['unprocessed_sales'] }}</h4>
                            <small class="text-warning">Unprocessed Sales</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="avtar avtar-s bg-light-primary">
                            <i class="ph-duotone ph-currency-circle-dollar"></i>
                        </div>
                        <div class="flex-grow-1">
                            <p class="mb-1 text-muted">Total Value</p>
                            <h4 class="mb-0 fw-bold">RM {{ number_format($this->salesStats['unprocessed_value'], 2) }}</h4>
                            <small class="text-primary">Pending Commission</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="avtar avtar-s bg-light-info">
                            <i class="ph-duotone ph-user-circle"></i>
                        </div>
                        <div class="flex-grow-1">
                            <p class="mb-1 text-muted">Preview Total Main</p>
                            <h4 class="mb-0 fw-bold">RM {{ number_format($previewTotalMainAgent, 2) }}</h4>
                            <small class="text-info">Main Agent Commission</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="avtar avtar-s bg-light-success">
                            <i class="ph-duotone ph-users"></i>
                        </div>
                        <div class="flex-grow-1">
                            <p class="mb-1 text-muted">Preview Total Agent</p>
                            <h4 class="mb-0 fw-bold">RM {{ number_format($previewTotalAgent, 2) }}</h4>
                            <small class="text-success">Agent Commission</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    {{-- Commission Generation Form and Preview --}}
    <div class="row">
        {{-- Commission Preview --}}
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h2 class="mb-0">Commission Preview by Cooperative</h2>
                            <p class="text-muted mb-0 mt-1">Select cooperatives to process</p>
                        </div>
                        @if (!empty($cooperativeBreakdown))
                            <div class="d-flex gap-2 align-items-center">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="selectAll"
                                           wire:model="selectAll" wire:click="toggleSelectAll">
                                    <label class="form-check-label" for="selectAll">
                                        Select All
                                    </label>
                                </div>
                                <button type="button" class="btn btn-sm btn-outline-primary"
                                        wire:click="setAllToDefaultCommission"
                                        wire:loading.attr="disabled"
                                        title="Set all cooperatives to use default commission percentages">
                                    <span wire:loading.remove wire:target="setAllToDefaultCommission">
                                        <i class="ph-duotone ph-percent me-1"></i>Set Default %
                                    </span>
                                    <span wire:loading wire:target="setAllToDefaultCommission">
                                        <i class="ph-duotone ph-spinner me-1"></i>Setting...
                                    </span>
                                </button>
                            </div>
                        @endif
                    </div>
                </div>
                        <div class="p-0 card-body">
                            <div class="p-3" style="max-height: 600px; overflow-y: auto;">
                                @if (!empty($cooperativeBreakdown))
                                    @foreach ($cooperativeBreakdown as $index => $cooperative)
                                        @php
                                            $cooperativeId = $cooperative['id'];
                                            $isSelected = $selectedCooperatives[$cooperativeId] ?? false;
                                            $mainAgentPerc = $cooperative['main_agent_percentage'];
                                            $agentPerc = $cooperative['agent_percentage'];
                                        @endphp
                                        <div class="p-3 mb-3 border rounded {{ $isSelected ? 'border-success bg-light' : ($index % 2 == 0 ? 'bg-light' : 'bg-white') }}">
                                            {{-- Checkbox Header --}}
                                            <div class="mb-2 d-flex align-items-center justify-content-between">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox"
                                                           id="coop_{{ $cooperativeId }}"
                                                           wire:click="toggleCooperativeSelection({{ $cooperativeId }})"
                                                           {{ $isSelected ? 'checked' : '' }}>
                                                    <label class="form-check-label fw-bold" for="coop_{{ $cooperativeId }}">
                                                    {{ $cooperative['name'] }}
                                                    </label>
                                                    </div>
                                                    @if ($isSelected)
                                                    <span class="badge bg-success">Selected</span>
                                                    @endif
                                                    </div>

                                                    <div class="row align-items-center">
                                                 <div class="col-12">
                                                            <small class="text-muted">
                                                            {{ $cooperative['sales_count'] }} sales • RM {{ number_format($cooperative['sales_value'], 2) }}
                                                    </small>
                                                    </div>
                                                    </div>

                                            {{-- Commission Display --}}
                                            <div class="mt-3 row">
                                            <div class="col-6">
                                            <div class="p-3 text-center border rounded">
                                            <small class="text-muted d-block">Main Agent ({{ $mainAgentPerc }}%)</small>
                                            <div class="fw-bold text-primary">
                                            RM {{ number_format($cooperative['main_agent_commission'], 2) }}
                                            </div>
                                            </div>
                                            </div>
                                            <div class="col-6">
                                            <div class="p-3 text-center border rounded">
                                            <small class="text-muted d-block">Agent ({{ $agentPerc }}%)</small>
                                            <div class="fw-bold text-success">
                                            RM {{ number_format($cooperative['agent_commission'], 2) }}
                                            </div>
                                            </div>
                                            </div>
                                            </div>
                                            <div class="mt-2 text-center">
                                            <small class="text-muted">Total: </small>
                                            <span class="fw-bold text-dark">
                                            RM {{ number_format($cooperative['total_commission'], 2) }}
                                            </span>
                                            </div>
                                        </div>
                                    @endforeach

                                    {{-- Grand Total Summary --}}
                                    <div class="p-4 mt-4 text-center border-top">
                                        <h5 class="mb-3 text-dark">Commission Summary</h5>
                                        <div class="row">
                                            <div class="col-6">
                                                <div class="p-3 border rounded">
                                                    <small class="text-muted d-block">Total Main Agent</small>
                                                    <h4 class="mb-0 text-primary">
                                                        RM {{ number_format($previewTotalMainAgent, 2) }}
                                                    </h4>
                                                </div>
                                            </div>
                                            <div class="col-6">
                                                <div class="p-3 border rounded">
                                                    <small class="text-muted d-block">Total Agent</small>
                                                    <h4 class="mb-0 text-success">
                                                        RM {{ number_format($previewTotalAgent, 2) }}
                                                    </h4>
                                                </div>
                                            </div>
                                        </div>
                                        <hr class="my-3">
                                        <div class="p-3 border rounded bg-light">
                                            <h6 class="mb-1 text-muted">Grand Total Commission</h6>
                                            <h3 class="mb-0 fw-bold">
                                                RM {{ number_format($previewTotalMainAgent + $previewTotalAgent, 2) }}
                                            </h3>
                                        </div>
                                    </div>
                                @else
                                    <div class="py-5 text-center">
                                        <div class="mx-auto mb-3 avtar avtar-xl bg-light-secondary">
                                            <i class="ph-duotone ph-info"></i>
                                        </div>
                                        <h5 class="text-muted">No Commission Data to Preview</h5>
                                        <p class="mb-0 text-muted">
                                            @if (!$lastCalculateDate)
                                                Select a date to see commission preview
                                            @else
                                                No unprocessed sales found for the selected date
                                            @endif
                                        </p>
                                    </div>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>

        {{-- Generation Form --}}
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <div>
                        <h2 class="mb-0">Commission Settings</h2>
                        <p class="text-muted mb-0 mt-1">Configure processing parameters</p>
                    </div>
                </div>
                <div class="card-body">
                    <div class="mb-4">
                        <label for="lastCalculateDate" class="form-label">
                            Last Calculate Date <span class="text-danger">*</span>
                        </label>
                        <input type="date" class="form-control form-control-lg" id="lastCalculateDate"
                               wire:model.live="lastCalculateDate" required>
                        <small class="text-muted">Commission will be calculated for sales up to this date only</small>
                    </div>

                    @if (!$lastCalculateDate)
                        <div class="alert alert-warning" role="alert">
                            Please select a last calculate date to see the preview.
                        </div>
                    @elseif (empty($cooperativeBreakdown))
                        <div class="alert alert-info" role="alert">
                            No unprocessed sales found for the selected date.
                        </div>
                    @else
                        <div class="alert alert-success" role="alert">
                            Ready to process {{ count($cooperativeBreakdown) }} cooperative(s) with
                            RM {{ number_format($previewTotalMainAgent + $previewTotalAgent, 2) }} total commission.
                        </div>
                    @endif

                    {{-- Action Buttons --}}
                    <div class="d-flex gap-2">
                        <button type="button" class="btn btn-success"
                                wire:click="processSelectedCooperatives"
                                wire:loading.attr="disabled"
                                @if (!$lastCalculateDate || empty($cooperativeBreakdown)) disabled @endif>
                            <span wire:loading.remove wire:target="processSelectedCooperatives">
                                <i class="ph-duotone ph-check-square me-2"></i>Process Selected Only
                            </span>
                            <span wire:loading wire:target="processSelectedCooperatives">
                                <i class="ph-duotone ph-spinner me-2"></i>Processing Selected...
                            </span>
                        </button>

                        <button type="button" class="btn btn-primary"
                                wire:click="processGeneration"
                                wire:loading.attr="disabled"
                                @if (!$lastCalculateDate || empty($cooperativeBreakdown)) disabled @endif>
                            <span wire:loading.remove wire:target="processGeneration">
                                <i class="ph-duotone ph-check me-2"></i>Process All Cooperatives
                            </span>
                            <span wire:loading wire:target="processGeneration">
                                <i class="ph-duotone ph-spinner me-2"></i>Processing All...
                            </span>
                        </button>
                    </div>

                    {{-- Processing Notice --}}
                    <div class="mt-3 alert alert-light" role="alert">
                        <h6 class="alert-heading">What happens when you generate?</h6>
                        <ul class="mb-0 small">
                            <li>All unprocessed sales up to the selected date will be processed</li>
                            <li>Commission amounts will be calculated based on tier settings</li>
                            <li>Distribution records will be created for agents and main agents</li>
                            <li>Sales status will be updated to "processed"</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

</div>
