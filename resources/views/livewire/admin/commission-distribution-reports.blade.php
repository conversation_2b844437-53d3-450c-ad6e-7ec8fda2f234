<div>
    <style>
        .table-responsive {
            overflow-x: auto !important;
            overflow-y: visible !important;
        }

        .dropdown-menu {
            position: absolute !important;
            z-index: 1050 !important;
        }

        .dropdown {
            position: static !important;
        }

        .card {
            overflow: visible !important;
        }
    </style>

    {{-- <PERSON> Header --}}
    <div class="page-header commission-page-header">
        <div class="page-block">
            <div class="row align-items-center">
                <div class="col-md-12">
                    <div class="page-header-title">
                        <h2 class="mb-0">Commission Distribution Reports</h2>
                        <p class="mt-2 mb-0 text-muted">Track and analyze commission distribution across agents and campaigns</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    {{-- Flash Messages --}}
    @if (session()->has('success'))
        <div class="border-0 alert alert-success alert-dismissible fade show" role="alert" style="border-radius: 12px;">
            <div class="d-flex align-items-start">
                <div class="flex-shrink-0 me-3">
                    <div class="avtar avtar-s bg-success" style="border-radius: 8px;">
                        <i class="ph-duotone ph-check-circle"></i>
                    </div>
                </div>
                <div class="flex-grow-1">
                    <div class="mb-1 fw-semibold text-success">Success!</div>
                    <div class="text-dark">{{ session('success') }}</div>
                </div>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        </div>
    @endif

    @if (session()->has('error'))
        <div class="border-0 alert alert-danger alert-dismissible fade show" role="alert" style="border-radius: 12px;">
            <div class="d-flex align-items-start">
                <div class="flex-shrink-0 me-3">
                    <div class="avtar avtar-s bg-danger" style="border-radius: 8px;">
                        <i class="ph-duotone ph-x-circle"></i>
                    </div>
                </div>
                <div class="flex-grow-1">
                    <div class="mb-1 fw-semibold text-danger">Error!</div>
                    <div class="text-dark">{{ session('error') }}</div>
                </div>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        </div>
    @endif

    {{-- Statistics Cards --}}
    <div class="mb-4 row">
        <div class="col-md-3">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="avtar avtar-s bg-light-primary">
                            <i class="ph-duotone ph-chart-pie"></i>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <p class="mb-1 text-muted">Total Distributions</p>
                            <h4 class="mb-0 fw-bold">{{ number_format($stats['total_distributions']) }}</h4>
                            <small class="text-primary">Commission records</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="avtar avtar-s bg-light-success">
                            <i class="ph-duotone ph-currency-circle-dollar"></i>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <p class="mb-1 text-muted">Total Commission</p>
                            <h4 class="mb-0 fw-bold">RM {{ number_format($stats['total_commission_amount'], 2) }}</h4>
                            <small class="text-success">All distributions</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="avtar avtar-s bg-light-info">
                            <i class="ph-duotone ph-users"></i>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <p class="mb-1 text-muted">Main Agent Commission</p>
                            <h4 class="mb-0 fw-bold">RM {{ number_format($stats['total_main_agent_commission'], 2) }}</h4>
                            <small class="text-info">Main agents total</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="avtar avtar-s bg-light-warning">
                            <i class="ph-duotone ph-user"></i>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <p class="mb-1 text-muted">Agent Commission</p>
                            <h4 class="mb-0 fw-bold">RM {{ number_format($stats['total_agent_commission'], 2) }}</h4>
                            <small class="text-warning">Agents total</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    {{-- Main Content Card --}}
    <div class="card">
        <div class="card-header">
            <div class="d-flex justify-content-between align-items-center">
                <div class="page-header-title">
                    <h2 class="mb-0">Commission Distribution Records</h2>
                    @if ($groupedData->total() > 0)
                        <p class="mt-1 mb-0 text-muted">Showing {{ $groupedData->firstItem() }} to
                            {{ $groupedData->lastItem() }} of {{ $groupedData->total() }} results</p>
                    @endif
                </div>
                <div class="page-header-breadcrumb">
                    <button wire:click="toggleFilters" class="btn btn-outline-secondary me-2">
                        <i class="ph-duotone ph-funnel me-2"></i>
                        {{ $showFilters ? 'Hide Filters' : 'Show Filters' }}
                    </button>
                    <button wire:click="clearFilters" class="btn btn-outline-primary">
                        <i class="ph-duotone ph-arrow-clockwise me-2"></i>Clear Filters
                    </button>
                </div>
            </div>
        </div>

        {{-- Advanced Filters Panel --}}
        @if ($showFilters)
            <div class="card-body border-bottom">
                <div class="p-4 bg-light" style="border-radius: 12px;">
                    <h5 class="mb-3 fw-semibold">
                        <i class="ph-duotone ph-funnel me-2"></i>Advanced Filters
                    </h5>
                    <div class="row g-3">
                        <div class="col-md-3">
                            <label class="form-label">Cooperative</label>
                            <select wire:model.live="cooperative_id" class="form-select">
                                <option value="">All Cooperatives</option>
                                @foreach($cooperatives as $cooperative)
                                    <option value="{{ $cooperative->id }}">{{ $cooperative->name }}</option>
                                @endforeach
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">Branch</label>
                            <select wire:model.live="branch_id" class="form-select">
                                <option value="">All Branches</option>
                                @foreach($branches as $branch)
                                    <option value="{{ $branch->id }}">{{ $branch->name }}</option>
                                @endforeach
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">Main Agent</label>
                            <select wire:model.live="main_agent_id" class="form-select">
                                <option value="">All Main Agents</option>
                                @foreach($mainAgents as $mainAgent)
                                    <option value="{{ $mainAgent->id }}">{{ $mainAgent->name }}</option>
                                @endforeach
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">Agent</label>
                            <select wire:model.live="agent_id" class="form-select">
                                <option value="">All Agents</option>
                                @foreach($agents as $agent)
                                    <option value="{{ $agent->id }}">{{ $agent->name }}</option>
                                @endforeach
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">Date From</label>
                            <input type="date" wire:model.live="date_from" class="form-control">
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">Date To</label>
                            <input type="date" wire:model.live="date_to" class="form-control">
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">Min Amount (RM)</label>
                            <input type="number" step="0.01" wire:model.live="min_amount" class="form-control" placeholder="0.00">
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">Max Amount (RM)</label>
                            <input type="number" step="0.01" wire:model.live="max_amount" class="form-control" placeholder="0.00">
                        </div>
                    </div>
                </div>
            </div>
        @endif

        {{-- Grouped Data Table --}}
        <div class="card-body">
            @if ($groupedData->count() > 0)
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th class="fw-semibold">Month</th>
                                <th class="fw-semibold">Cooperative</th>
                                <th class="fw-semibold">Total Distributions</th>
                                <th class="fw-semibold">Main Agent Commission</th>
                                <th class="fw-semibold">Agent Commission</th>
                                <th class="fw-semibold">Total Commission</th>
                                <th class="fw-semibold">Period</th>
                                <th class="text-center fw-semibold">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach ($groupedData as $group)
                                <tr>
                                    <td>
                                        <div class="fw-semibold text-primary">
                                            {{ \Carbon\Carbon::createFromFormat('Y-m', $group->month)->format('F Y') }}
                                        </div>
                                        <small class="text-muted">{{ $group->month }}</small>
                                    </td>
                                    <td>
                                        <div class="fw-semibold">{{ $group->cooperative_name }}</div>
                                    </td>
                                    <td>
                                        <span class="badge bg-light-info fs-6">{{ number_format($group->total_distributions) }}</span>
                                    </td>
                                    <td>
                                        <div class="text-success fw-semibold">
                                            RM {{ number_format($group->total_main_agent_commission, 2) }}
                                        </div>
                                    </td>
                                    <td>
                                        <div class="text-info fw-semibold">
                                            RM {{ number_format($group->total_agent_commission, 2) }}
                                        </div>
                                    </td>
                                    <td>
                                        <div class="text-primary fw-bold">
                                            RM {{ number_format($group->total_commission, 2) }}
                                        </div>
                                    </td>
                                    <td>
                                        <div class="small">
                                            <div>{{ \Carbon\Carbon::parse($group->first_distribution_date)->format('d M') }}</div>
                                            <div class="text-muted">to {{ \Carbon\Carbon::parse($group->last_distribution_date)->format('d M Y') }}</div>
                                        </div>
                                    </td>
                                    <td class="text-center">
                                        <button wire:click="viewDetails('{{ $group->month }}', {{ $group->acs_coorperative_id }}, '{{ $group->cooperative_name }}')"
                                                class="btn btn-sm btn-outline-primary">
                                            <i class="ph-duotone ph-eye me-1"></i>View Details
                                        </button>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>

                {{-- Pagination Controls --}}
                <div class="mt-4 d-flex justify-content-between align-items-center">
                    <div class="d-flex align-items-center">
                        <label class="mb-0 form-label me-2">Show:</label>
                        <select wire:model.live="perPage" class="form-select form-select-sm" style="width: auto;">
                            <option value="10">10</option>
                            <option value="15">15</option>
                            <option value="25">25</option>
                            <option value="50">50</option>
                        </select>
                        <span class="ms-2 text-muted">entries per page</span>
                    </div>
                    <div>
                        {{ $groupedData->links() }}
                    </div>
                </div>
            @else
                {{-- Empty State --}}
                <div class="py-5 text-center">
                    <div class="mx-auto mb-4 avtar avtar-xl bg-light-secondary" style="border-radius: 16px;">
                        <i class="ph-duotone ph-chart-pie text-secondary" style="font-size: 2rem;"></i>
                    </div>
                    <h4 class="mb-3">No Commission Distributions Found</h4>
                    <p class="mb-4 text-muted">
                        @if($showFilters && ($cooperative_id || $branch_id || $main_agent_id || $agent_id || $date_from || $date_to || $min_amount || $max_amount))
                            No distributions match your current filter criteria.<br>
                            Try adjusting your filters or clear them to see all distributions.
                        @else
                            There are no commission distributions to display at the moment.<br>
                            Distributions will appear here once sales commissions are processed.
                        @endif
                    </p>
                    @if($showFilters)
                        <button wire:click="clearFilters" class="btn btn-primary">
                            <i class="ph-duotone ph-arrow-clockwise me-2"></i>Clear All Filters
                        </button>
                    @endif
                </div>
            @endif
        </div>
    </div>

    {{-- Detail Modal --}}
    @if ($showDetailModal)
        <div class="modal fade show" tabindex="-1" style="display: block; background-color: rgba(0,0,0,0.5);"
             wire:ignore.self wire:click="closeDetailModal">
            <div class="modal-dialog modal-xl" wire:click.stop>
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">
                            <i class="ph-duotone ph-list-bullets me-2"></i>
                            Commission Distribution Details -
                            {{ \Carbon\Carbon::createFromFormat('Y-m', $selectedMonth)->format('F Y') }}
                        </h5>
                        <button type="button" class="btn-close" wire:click="closeDetailModal"></button>
                    </div>
                    <div class="modal-body" style="max-height: 70vh; overflow-y: auto;">
                        @if ($detailData->count() > 0)
                            <div class="mb-3">
                                <div class="alert alert-info">
                                    <i class="ph-duotone ph-info me-2"></i>
                                    Showing {{ $detailData->count() }} distribution records for
                                    <strong>{{ $detailData->first()->cooperative->name ?? 'Unknown Cooperative' }}</strong>
                                    in <strong>{{ \Carbon\Carbon::createFromFormat('Y-m', $selectedMonth)->format('F Y') }}</strong>
                                </div>
                            </div>

                            <div class="table-responsive">
                                <table class="table table-hover table-sm">
                                    <thead class="table-light">
                                        <tr>
                                            <th>ID</th>
                                            <th>Branch</th>
                                            <th>Main Agent</th>
                                            <th>Agent</th>
                                            <th>Main Agent Commission</th>
                                            <th>Agent Commission</th>
                                            <th>Total Commission</th>
                                            <th>Date</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach ($detailData as $detail)
                                            <tr>
                                                <td>
                                                    <span class="badge bg-light-secondary">#{{ $detail->id }}</span>
                                                </td>
                                                <td>
                                                    <div class="fw-semibold">{{ $detail->cooperativeBranch->name ?? 'N/A' }}</div>
                                                </td>
                                                <td>
                                                    @if($detail->mainAgent)
                                                        <div>
                                                            <div class="fw-semibold">{{ $detail->mainAgent->name }}</div>
                                                            <small class="text-muted">{{ $detail->mainAgent->email }}</small>
                                                        </div>
                                                    @else
                                                        <span class="text-muted">N/A</span>
                                                    @endif
                                                </td>
                                                <td>
                                                    @if($detail->agent)
                                                        <div>
                                                            <div class="fw-semibold">{{ $detail->agent->name }}</div>
                                                            <small class="text-muted">{{ $detail->agent->email }}</small>
                                                        </div>
                                                    @else
                                                        <span class="text-muted">N/A</span>
                                                    @endif
                                                </td>
                                                <td>
                                                    <div class="text-success fw-semibold">
                                                        RM {{ number_format($detail->main_agent_commission_amount, 2) }}
                                                    </div>
                                                    <small class="text-muted">{{ number_format($detail->main_agent_percentage, 2) }}%</small>
                                                </td>
                                                <td>
                                                    <div class="text-info fw-semibold">
                                                        RM {{ number_format($detail->agent_commission_amount, 2) }}
                                                    </div>
                                                    <small class="text-muted">{{ number_format($detail->agent_percentage, 2) }}%</small>
                                                </td>
                                                <td>
                                                    <div class="text-primary fw-bold">
                                                        RM {{ number_format($detail->main_agent_commission_amount + $detail->agent_commission_amount, 2) }}
                                                    </div>
                                                </td>
                                                <td>
                                                    <div>{{ $detail->created_at->format('d M Y') }}</div>
                                                    <small class="text-muted">{{ $detail->created_at->format('H:i') }}</small>
                                                </td>
                                            </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>

                            {{-- Summary for this selection --}}
                            <div class="p-3 mt-4 rounded bg-light">
                                <h6 class="mb-3">Summary for this period:</h6>
                                <div class="row">
                                    <div class="col-md-3">
                                        <div class="text-center">
                                            <div class="text-muted">Total Records</div>
                                            <div class="fw-bold fs-5">{{ $detailData->count() }}</div>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="text-center">
                                            <div class="text-muted">Main Agent Total</div>
                                            <div class="fw-bold fs-5 text-success">RM {{ number_format($detailData->sum('main_agent_commission_amount'), 2) }}</div>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="text-center">
                                            <div class="text-muted">Agent Total</div>
                                            <div class="fw-bold fs-5 text-info">RM {{ number_format($detailData->sum('agent_commission_amount'), 2) }}</div>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="text-center">
                                            <div class="text-muted">Grand Total</div>
                                            <div class="fw-bold fs-5 text-primary">RM {{ number_format($detailData->sum('main_agent_commission_amount') + $detailData->sum('agent_commission_amount'), 2) }}</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @else
                            <div class="py-5 text-center">
                                <div class="mx-auto mb-4 avtar avtar-xl bg-light-secondary">
                                    <i class="ph-duotone ph-database text-secondary" style="font-size: 2rem;"></i>
                                </div>
                                <h5>No Details Found</h5>
                                <p class="text-muted">No distribution details found for the selected period.</p>
                            </div>
                        @endif
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" wire:click="closeDetailModal">
                            <i class="ph-duotone ph-x me-1"></i>Close
                        </button>
                    </div>
                </div>
            </div>
        </div>
    @endif
</div>
