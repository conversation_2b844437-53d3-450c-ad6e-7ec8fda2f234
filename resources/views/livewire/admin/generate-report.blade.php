<div>
    {{-- <PERSON> Header --}}
    <div class="page-header">
        <div class="page-block">
            <div class="row align-items-center">
                <div class="col-md-12">
                    <div class="page-header-title">
                        <h2 class="mb-0">Sales & Commission Management</h2>
                        <p class="text-muted mb-0 mt-2">Generate and view commission reports from sales data</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    {{-- Flash Messages --}}
    @if (session()->has('success'))
        <div class="alert alert-success alert-dismissible fade show border-0" role="alert" style="border-radius: 12px;">
            <div class="d-flex align-items-start">
                <div class="flex-shrink-0 me-3">
                    <div class="avtar avtar-s bg-success" style="border-radius: 8px;">
                        <i class="ph-duotone ph-check-circle"></i>
                    </div>
                </div>
                <div class="flex-grow-1">
                    <div class="fw-semibold text-success mb-1">Success!</div>
                    <div class="text-dark">{{ session('success') }}</div>
                </div>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        </div>
    @endif

    @if (session()->has('error'))
        <div class="alert alert-danger alert-dismissible fade show border-0" role="alert" style="border-radius: 12px;">
            <div class="d-flex align-items-start">
                <div class="flex-shrink-0 me-3">
                    <div class="avtar avtar-s bg-danger" style="border-radius: 8px;">
                        <i class="ph-duotone ph-x-circle"></i>
                    </div>
                </div>
                <div class="flex-grow-1">
                    <div class="fw-semibold text-danger mb-1">Error!</div>
                    <div class="text-dark">{{ session('error') }}</div>
                </div>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        </div>
    @endif

    {{-- Navigation Tabs --}}
    <div class="card mb-4">
        <div class="card-header">
            <ul class="nav nav-pills">
                <li class="nav-item">
                    <a class="nav-link {{ $activeTab === 'sales' ? 'active' : '' }}"
                       href="#" wire:click.prevent="setActiveTab('sales')">
                        <i class="ph-duotone ph-clock me-2"></i>Unprocessed Sales
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {{ $activeTab === 'history' ? 'active' : '' }}"
                       href="#" wire:click.prevent="setActiveTab('history')">
                        <i class="ph-duotone ph-check-circle me-2"></i>Processed Sales
                    </a>
                </li>
            </ul>
        </div>
    </div>





    {{-- Sales List Tab Content --}}
    @if ($activeTab === 'sales')
        {{-- Statistics Cards --}}
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="avtar avtar-s bg-light-info">
                                <i class="ph-duotone ph-shopping-cart"></i>
                            </div>
                            <div class="flex-grow-1">
                                <p class="mb-1 text-muted">Total Sales</p>
                                <h4 class="mb-0 fw-bold">{{ $this->salesStats['total_sales'] }}</h4>
                                <small class="text-info">All Records</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="avtar avtar-s bg-light-success">
                                <i class="ph-duotone ph-check-circle"></i>
                            </div>
                            <div class="flex-grow-1">
                                <p class="mb-1 text-muted">Processed</p>
                                <h4 class="mb-0 fw-bold">{{ $this->salesStats['processed_sales'] }}</h4>
                                <small class="text-success">Commission Generated</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="avtar avtar-s bg-light-warning">
                                <i class="ph-duotone ph-clock"></i>
                            </div>
                            <div class="flex-grow-1">
                                <p class="mb-1 text-muted">Unprocessed</p>
                                <h4 class="mb-0 fw-bold">{{ $this->salesStats['unprocessed_sales'] }}</h4>
                                <small class="text-warning">Pending Commission</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="avtar avtar-s bg-light-primary">
                                <i class="ph-duotone ph-currency-circle-dollar"></i>
                            </div>
                            <div class="flex-grow-1">
                                <p class="mb-1 text-muted">Unprocessed Value</p>
                                <h4 class="mb-0 fw-bold">RM {{ number_format($this->salesStats['unprocessed_value'], 2) }}</h4>
                                <small class="text-primary">Pending Sales</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        {{-- Search and Filter Section --}}
        <div class="card mb-4">
            <div class="card-body">
                <div class="row g-3">
                    <div class="col-md-5">
                        <label for="searchTerm" class="form-label">Search</label>
                        <input type="text" class="form-control" id="searchTerm"
                               wire:model.live="searchTerm"
                               placeholder="Search by user, variety name, variety type...">
                    </div>
                    <div class="col-md-4">
                        <label for="dateTo" class="form-label">Date To</label>
                        <input type="date" class="form-control" id="dateTo"
                               wire:model.live="dateTo">
                    </div>
                    <div class="col-md-3 d-flex align-items-end">
                        <button class="btn btn-outline-secondary" wire:click="$set('searchTerm', ''); $set('dateTo', '')">
                            <i class="ph-duotone ph-x me-2"></i>Clear Filters
                        </button>
                    </div>
                </div>
            </div>
        </div>

        {{-- Sales List Table --}}
        <div class="card">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h2 class="mb-0">Unprocessed Sales Records</h2>
                        <p class="text-muted mb-0 mt-1">{{ $this->salesList->total() }} unprocessed records</p>
                    </div>
                    <span class="badge bg-warning">{{ $this->salesList->total() }} Unprocessed</span>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>User</th>
                                <th>Variety</th>
                                <th>Quantity</th>
                                <th>Total Price</th>
                                <th>Commission %</th>
                                <th>Commission Amount</th>
                                <th>Main Agent Commission</th>
                                <th>Agent Commission</th>
                                <th>Split %</th>
                                <th>Date Range</th>
                            </tr>
                        </thead>
                                        <tbody>
                                            @foreach ($this->salesList as $sale)
                                                @php
                                                    $commission = $this->calculateSaleCommission($sale);
                                                @endphp
                                                <tr>
                                                    <td>
                                                        <div class="small">
                                                            <strong>{{ $sale->user_name }}</strong><br>
                                                            <span class="text-muted">{{ $sale->user_email }}</span><br>
                                                            <small class="text-info">{{ $sale->cooperative_name ?? 'Coop ID: ' . $sale->acs_coorperative_id }}</small>
                                                        </div>
                                                    </td>
                                                    <td>
                                                        <span class="badge bg-secondary">{{ $sale->variety_name ?? $sale->variety_type }}</span>
                                                    </td>
                                                    <td class="text-center">
                                                        <span class="badge bg-info">{{ $sale->quantity }}</span>
                                                    </td>
                                                    <td class="text-end">RM {{ number_format($sale->total_price, 2) }}</td>
                                                    <td class="text-center">{{ $commission['commission_percentage'] }}%</td>
                                                    <td class="text-end">RM {{ number_format($commission['commission_amount'], 2) }}</td>
                                                    <td class="text-end">RM {{ number_format($commission['master_agent_commission'], 2) }}</td>
                                                    <td class="text-end">RM {{ number_format($commission['agent_commission'], 2) }}</td>
                                                    <td class="text-center">
                                                        <small class="text-muted">
                                                            <span class="badge bg-primary">{{ $commission['main_agent_percentage'] }}%</span> /
                                                            <span class="badge bg-info">{{ $commission['agent_percentage'] }}%</span>
                                                        </small>
                                                    </td>
                                                    <td class="small">
                                                        @if ($sale->first_sale_date == $sale->last_sale_date)
                                                            {{ \Carbon\Carbon::parse($sale->first_sale_date)->format('M d, Y') }}
                                                        @else
                                                            {{ \Carbon\Carbon::parse($sale->first_sale_date)->format('M d') }} -
                                                            {{ \Carbon\Carbon::parse($sale->last_sale_date)->format('M d, Y') }}
                                                        @endif
                                                    </td>
                                                </tr>
                                            @endforeach
                                        </tbody>
                                    </table>
                                </div>
                            </div>
            <div class="card-footer">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        {{ $this->salesList->links() }}
                    </div>
                    @if ($this->salesStats['unprocessed_sales'] > 0 && !$isSubmitted)
                        <div>
                            <a href="{{ route('admin.commission-generation') }}" class="btn btn-primary">
                                <i class="ph-duotone ph-calculator me-2"></i>Generate Commission Report
                            </a>
                            <small class="mt-1 text-muted d-block">
                                {{ $this->salesStats['unprocessed_sales'] }} unprocessed sales ready for commission generation
                            </small>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    @endif



    {{-- History Tab Content --}}
    @if ($activeTab === 'history')
        {{-- History Statistics Cards --}}
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="avtar avtar-s bg-light-info">
                                <i class="ph-duotone ph-check-circle"></i>
                            </div>
                            <div class="flex-grow-1">
                                <p class="mb-1 text-muted">Processed Sales</p>
                                <h4 class="mb-0 fw-bold">{{ $this->historyStats['processed_sales'] }}</h4>
                                <small class="text-info">Complete records</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="avtar avtar-s bg-light-success">
                                <i class="ph-duotone ph-currency-circle-dollar"></i>
                            </div>
                            <div class="flex-grow-1">
                                <p class="mb-1 text-muted">Total Value</p>
                                <h4 class="mb-0 fw-bold">RM {{ number_format($this->historyStats['total_value'], 2) }}</h4>
                                <small class="text-success">Total sales value</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="avtar avtar-s bg-light-primary">
                                <i class="ph-duotone ph-user-circle"></i>
                            </div>
                            <div class="flex-grow-1">
                                <p class="mb-1 text-muted">Main Agent Total</p>
                                <h4 class="mb-0 fw-bold">RM {{ number_format($this->historyStats['total_main_agent'], 2) }}</h4>
                                <small class="text-primary">Commission earned</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="avtar avtar-s bg-light-warning">
                                <i class="ph-duotone ph-users"></i>
                            </div>
                            <div class="flex-grow-1">
                                <p class="mb-1 text-muted">Agent Total</p>
                                <h4 class="mb-0 fw-bold">RM {{ number_format($this->historyStats['total_agent'], 2) }}</h4>
                                <small class="text-warning">Commission earned</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        {{-- Search and Filter Section --}}
        <div class="card mb-4">
            <div class="card-body">
                <div class="row g-3">
                    <div class="col-md-4">
                        <label for="searchTerm" class="form-label">Search</label>
                        <input type="text" class="form-control" id="searchTerm"
                               wire:model.live="searchTerm"
                               placeholder="Search by user, variety name, variety type...">
                    </div>
                    <div class="col-md-3">
                        <label for="dateTo" class="form-label">Created Date To</label>
                        <input type="date" class="form-control" id="dateTo"
                               wire:model.live="dateTo">
                    </div>
                    <div class="col-md-3">
                        <label for="processedDateTo" class="form-label">Processed Date To</label>
                        <input type="date" class="form-control" id="processedDateTo"
                               wire:model.live="processedDateTo">
                    </div>
                    <div class="col-md-2 d-flex align-items-end">
                        <button class="btn btn-outline-secondary" wire:click="$set('searchTerm', ''); $set('dateTo', ''); $set('processedDateTo', '')">
                            <i class="ph-duotone ph-x me-2"></i>Clear Filters
                        </button>
                    </div>
                </div>
            </div>
        </div>

        {{-- Processed Sales History Table --}}
        <div class="card">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h2 class="mb-0">Processed Sales History</h2>
                        <p class="text-muted mb-0 mt-1">{{ $this->commissionHistory->total() }} processed records</p>
                    </div>
                    <span class="badge bg-success">{{ $this->commissionHistory->total() }} Processed</span>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>User</th>
                                <th>Variety</th>
                                <th>Quantity</th>
                                <th>Total Price</th>
                                <th>Commission %</th>
                                <th>Commission Amount</th>
                                <th>Main Agent Commission</th>
                                <th>Agent Commission</th>
                                <th>Split %</th>
                                <th>Processed Date</th>
                            </tr>
                        </thead>
                                        <tbody>
                                            @foreach ($this->commissionHistory as $sale)
                                                @php
                                                    $commission = $this->calculateSaleCommission($sale);
                                                @endphp
                                                <tr>
                                                    <td>
                                                        <div class="small">
                                                            <strong>{{ $sale->user_name }}</strong><br>
                                                            <span class="text-muted">{{ $sale->user_email }}</span><br>
                                                            <small class="text-info">{{ $sale->cooperative_name ?? 'Coop ID: ' . $sale->acs_coorperative_id }}</small>
                                                        </div>
                                                    </td>
                                                    <td>
                                                        <span class="badge bg-secondary">{{ $sale->variety_name ?? $sale->variety_type }}</span>
                                                    </td>
                                                    <td class="text-center">
                                                        <span class="badge bg-success">{{ $sale->quantity }}</span>
                                                    </td>
                                                    <td class="text-end">RM {{ number_format($sale->total_price, 2) }}</td>
                                                    <td class="text-center">{{ $commission['commission_percentage'] }}%</td>
                                                    <td class="text-end">RM {{ number_format($commission['commission_amount'], 2) }}</td>
                                                    <td class="text-end">RM {{ number_format($commission['master_agent_commission'], 2) }}</td>
                                                    <td class="text-end">RM {{ number_format($commission['agent_commission'], 2) }}</td>
                                                    <td class="text-center">
                                                        <small class="text-muted">
                                                            <span class="badge bg-primary">{{ $commission['main_agent_percentage'] }}%</span> /
                                                            <span class="badge bg-info">{{ $commission['agent_percentage'] }}%</span>
                                                        </small>
                                                    </td>
                                                    <td class="small">
                                                        @if ($sale->processed_date)
                                                            {{ \Carbon\Carbon::parse($sale->processed_date)->format('M d, Y H:i') }}
                                                        @else
                                                            {{ \Carbon\Carbon::parse($sale->last_sale_date)->format('M d, Y') }}
                                                        @endif
                                                    </td>
                                                </tr>
                                            @endforeach
                                        </tbody>
                                    </table>
                                </div>
                            </div>
            <div class="card-footer">
                {{ $this->commissionHistory->links() }}
            </div>
        </div>
    @endif {{-- End History Tab --}}

</div>
