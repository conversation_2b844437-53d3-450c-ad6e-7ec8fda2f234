<div>
    <style>
        .table-responsive {
            overflow-x: auto !important;
            overflow-y: visible !important;
        }
        
        /* iOS-style toggle switches */
        .form-switch .form-check-input {
            width: 3rem;
            height: 1.5rem;
            background-color: #dee2e6;
            border: none;
            border-radius: 1rem;
            background-image: none;
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
        }
        
        .form-switch .form-check-input:checked {
            background-color: #1976d2;
            border-color: #1976d2;
        }
        
        .form-switch .form-check-input:focus {
            box-shadow: 0 0 0 0.2rem rgba(25, 118, 210, 0.25);
        }
        
        .form-switch .form-check-input::before {
            content: '';
            position: absolute;
            top: 2px;
            left: 2px;
            width: 1.25rem;
            height: 1.25rem;
            background-color: white;
            border-radius: 50%;
            transition: all 0.3s ease;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }
        
        .form-switch .form-check-input:checked::before {
            transform: translateX(1.5rem);
        }
        
        .dropdown-menu {
            position: absolute !important;
            z-index: 1050 !important;
        }
        .dropdown {
            position: static !important;
        }
        .card {
            overflow: visible !important;
        }
        .sales-sortable {
            cursor: pointer;
            transition: all 0.2s;
        }
        .sales-sortable:hover {
            background-color: #f8f9fa;
        }
        .sales-sortable.active {
            background-color: #e3f2fd;
        }
        .sales-status-badge {
            font-size: 0.75rem;
            padding: 0.25rem 0.75rem;
            border-radius: 50px;
        }
    </style>

    {{-- Page Header --}}
    <div class="page-header sales-page-header">
        <div class="page-block">
            <div class="row align-items-center">
                <div class="col-md-12">
                    <div class="page-header-title">
                        <h2 class="mb-0">Sales Management</h2>
                        <p class="text-muted mb-0 mt-2">Manage and view all sales records with agent and branch information</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    {{-- Flash Messages --}}
    @if (session()->has('success'))
        <div class="alert alert-success alert-dismissible fade show border-0" role="alert" style="border-radius: 12px;">
            <div class="d-flex align-items-start">
                <div class="flex-shrink-0 me-3">
                    <div class="avtar avtar-s bg-success" style="border-radius: 8px;">
                        <i class="ph-duotone ph-check-circle"></i>
                    </div>
                </div>
                <div class="flex-grow-1">
                    <div class="fw-semibold text-success mb-1">Success!</div>
                    <div class="text-dark">{{ session('success') }}</div>
                </div>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        </div>
    @endif

    @if (session()->has('error'))
        <div class="alert alert-danger alert-dismissible fade show border-0" role="alert" style="border-radius: 12px;">
            <div class="d-flex align-items-start">
                <div class="flex-shrink-0 me-3">
                    <div class="avtar avtar-s bg-danger" style="border-radius: 8px;">
                        <i class="ph-duotone ph-x-circle"></i>
                    </div>
                </div>
                <div class="flex-grow-1">
                    <div class="fw-semibold text-danger mb-1">Error!</div>
                    <div class="text-dark">{{ session('error') }}</div>
                </div>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        </div>
    @endif

    {{-- Statistics Cards --}}
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="avtar avtar-s bg-light-primary">
                            <i class="ph-duotone ph-shopping-cart"></i>
                        </div>
                        <div class="flex-grow-1">
                            <p class="mb-1 text-muted">Total Sales</p>
                            <h4 class="mb-0 fw-bold">{{ number_format($salesSummary['total_sales']) }}</h4>
                            <small class="text-primary">All sales records</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="avtar avtar-s bg-light-success">
                            <i class="ph-duotone ph-currency-circle-dollar"></i>
                        </div>
                        <div class="flex-grow-1">
                            <p class="mb-1 text-muted">Total Value</p>
                            <h4 class="mb-0 fw-bold">RM {{ number_format($salesSummary['total_value'], 2) }}</h4>
                            <small class="text-success">Sales revenue</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="avtar avtar-s bg-light-info">
                            <i class="ph-duotone ph-chart-line"></i>
                        </div>
                        <div class="flex-grow-1">
                            <p class="mb-1 text-muted">Average Sale Value</p>
                            <h4 class="mb-0 fw-bold">RM {{ number_format($salesSummary['avg_sale_value'], 2) }}</h4>
                            <small class="text-info">Per transaction</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="avtar avtar-s bg-light-warning">
                            <i class="ph-duotone ph-users"></i>
                        </div>
                        <div class="flex-grow-1">
                            <p class="mb-1 text-muted">Active Agents</p>
                            <h4 class="mb-0 fw-bold">{{ count($agents) }}</h4>
                            <small class="text-warning">Sales agents</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    {{-- Main Content Card --}}
    <div class="card">
        <div class="card-header">
            <div class="d-flex justify-content-between align-items-center">
                <div class="page-header-title">
                    <h2 class="mb-0">Sales Records</h2>
                    @if($sales->total() > 0)
                        <p class="text-muted mb-0 mt-1">Showing {{ $sales->firstItem() }} to {{ $sales->lastItem() }} of {{ $sales->total() }} results</p>
                    @endif
                </div>
                <div class="page-header-breadcrumb">
                    <button wire:click="toggleFilters" class="btn btn-outline-secondary me-2">
                        <i class="ph-duotone ph-funnel me-2"></i>
                        {{ $showFilters ? 'Hide Filters' : 'Show Filters' }}
                    </button>
                </div>
            </div>
        </div>

        {{-- Advanced Filters Panel --}}
        @if($showFilters)
        <div class="card-body border-bottom">
            <div class="row g-3">
                <div class="col-md-4">
                    <label class="form-label">Search Sales</label>
                    <input wire:model.live.debounce.300ms="search" class="form-control" type="text" placeholder="Search by sale ID, product, agent..." />
                </div>
                <div class="col-md-4">
                    <label class="form-label">Cooperative</label>
                    <select wire:model.live="selectedCooperative" class="form-select">
                        <option value="">All Cooperatives</option>
                        @foreach($cooperatives as $id => $name)
                            <option value="{{ $id }}">{{ $name }}</option>
                        @endforeach
                    </select>
                </div>
                <div class="col-md-4">
                    <label class="form-label">Agent</label>
                    <select wire:model.live="selectedAgent" class="form-select">
                        <option value="">All Agents</option>
                        @foreach($agents as $id => $name)
                            <option value="{{ $id }}">{{ $name }}</option>
                        @endforeach
                    </select>
                </div>
            </div>
            <div class="row mt-3">
                <div class="col-12">
                    <button wire:click="clearFilters" class="btn btn-outline-secondary">
                        <i class="ph-duotone ph-x me-2"></i>Clear Filters
                    </button>
                </div>
            </div>
        </div>
        @endif

        <div class="card-body" style="overflow: visible;">
            {{-- Per Page Selector --}}
            <div class="row mb-3">
                <div class="col-md-6">
                    <div class="d-flex align-items-center">
                        <label class="me-2 mb-0">Show:</label>
                        <select wire:model.live="perPage" class="form-select" style="width: auto;">
                            <option value="10">10</option>
                            <option value="25">25</option>
                            <option value="50">50</option>
                            <option value="100">100</option>
                        </select>
                        <span class="ms-2">entries</span>
                    </div>
                </div>
            </div>

            {{-- Sales Table --}}
            <div class="table-responsive" style="overflow: visible;">
                <table class="table table-hover" id="salesTable">
                    <thead>
                        <tr>
                            <th>Sale Details</th>
                            <th>Product</th>
                            <th>Agent</th>
                            <th>Cooperative & Branch</th>
                            <th>Price</th>
                            <th>Date</th>
                            <th class="text-center">Actions</th>
                        </tr>
                    </thead>
                            <tbody>
                                @forelse($sales as $sale)
                                    <tr class="align-middle">
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="avtar avtar-s bg-light-primary">
                                                    <i class="ph-duotone ph-hash"></i>
                                                </div>
                                                <div class="ms-2">
                                                    <h6 class="mb-0">{{ $sale->{'23_senarai_jualan_id'} }}</h6>
                                                    <small class="text-muted">{{ substr($sale->id, 0, 8) }}...</small>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="avtar avtar-s bg-light-warning">
                                                    <i class="ph-duotone ph-package"></i>
                                                </div>
                                                <div class="ms-2">
                                                    <h6 class="mb-0">{{ $sale->variety_name ?? $sale->variety_type }}</h6>
                                                    <small class="text-muted">{{ $sale->sku_number }}</small>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="avtar avtar-s bg-light-info">
                                                    <i class="ph-duotone ph-user"></i>
                                                </div>
                                                <div class="ms-2">
                                                    <h6 class="mb-0">{{ $sale->agent_name }}</h6>
                                                    <small class="text-muted">{{ $sale->agent_email }}</small>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="avtar avtar-s bg-light-success">
                                                    <i class="ph-duotone ph-buildings"></i>
                                                </div>
                                                <div class="ms-2">
                                                    <h6 class="mb-0">{{ $sale->cooperative_name ?? 'N/A' }}</h6>
                                                    <small class="text-muted">{{ $sale->branch_name ?? 'N/A' }}</small>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="avtar avtar-s bg-light-success">
                                                    <i class="ph-duotone ph-currency-circle-dollar"></i>
                                                </div>
                                                <div class="ms-2">
                                                    <h6 class="mb-0 fw-bold text-success">RM {{ number_format($sale->total_price, 2) }}</h6>
                                                    <small class="text-muted">Total price</small>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="fw-medium">{{ \Carbon\Carbon::parse($sale->created_at)->format('M d, Y') }}</span>
                                            <small class="text-muted d-block">{{ \Carbon\Carbon::parse($sale->created_at)->format('H:i') }}</small>
                                        </td>
                                        <td>
                                            <div class="dropdown">
                                                <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                                                    <i class="ph-duotone ph-dots-three-vertical"></i>
                                                </button>
                                                <ul class="dropdown-menu dropdown-menu-end">
                                                    <li>
                                                        <button wire:click="showSaleDetails('{{ $sale->id }}')" class="dropdown-item">
                                                            <i class="ph-duotone ph-eye me-2"></i>View Details
                                                        </button>
                                                    </li>
                                                </ul>
                                            </div>
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="7" class="text-center py-4">
                                            <div class="py-4 text-center">
                                                <div class="mx-auto mb-3 avtar avtar-xl bg-light-secondary">
                                                    <i class="ph-duotone ph-shopping-cart"></i>
                                                </div>
                                                <h5>No Sales Records Found</h5>
                                                <p class="text-muted">No sales records match your current search criteria.</p>
                                            </div>
                                        </td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>

            {{-- Pagination --}}
            @if($sales->hasPages())
                <div class="d-flex justify-content-between align-items-center mt-4">
                    <div class="text-muted">
                        Showing {{ $sales->firstItem() }} to {{ $sales->lastItem() }} of {{ $sales->total() }} results
                    </div>
                    <div>
                        {{ $sales->links('pagination::bootstrap-4') }}
                    </div>
                </div>
            @endif
        </div>
    </div>

    {{-- Sale Details Modal --}}
    @if($showModal && $selectedSale)
    <div class="modal fade show" style="display: block; background-color: rgba(0,0,0,0.4);" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content border-0" style="border-radius: 16px;">
                <div class="modal-header border-0 pb-0">
                    <button wire:click="closeModal" type="button" class="btn-close" style="position: absolute; top: 1rem; right: 1rem; z-index: 10;"></button>
                </div>
                <div class="modal-body px-4 py-5">
                    <div class="text-center mb-4">
                        <div class="mx-auto mb-4" style="width: 80px; height: 80px; border-radius: 50%; background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%); display: flex; align-items: center; justify-content: center;">
                            <i class="ph-duotone ph-shopping-cart" style="font-size: 3rem; color: #1976d2;"></i>
                        </div>
                        <h4 class="mb-3 fw-bold text-dark">Sale Details</h4>
                        <p class="text-muted mb-4">Complete information about this sale record</p>
                    </div>
                    <div class="row">
                        <div class="col-12">
                            <table class="table table-borderless">
                                <tr>
                                    <td class="fw-medium">Sale ID:</td>
                                    <td>{{ $selectedSale->{'23_senarai_jualan_id'} }}</td>
                                </tr>
                                <tr>
                                    <td class="fw-medium">Product:</td>
                                    <td style="word-wrap: break-word; white-space: normal;">{{ $selectedSale->variety_name ?? $selectedSale->variety_type }}</td>
                                </tr>
                                <tr>
                                    <td class="fw-medium">SKU:</td>
                                    <td>{{ $selectedSale->sku_number }}</td>
                                </tr>
                                <tr>
                                    <td class="fw-medium">Category:</td>
                                    <td>{{ $selectedSale->design_code }}</td>
                                </tr>
                                <tr>
                                    <td class="fw-medium">Purity:</td>
                                    <td><span class="badge {{ $selectedSale->purity == '999' ? 'bg-warning' : 'bg-info' }}">{{ $selectedSale->purity }}</span></td>
                                </tr>
                                <tr>
                                    <td class="fw-medium">Price:</td>
                                    <td class="fw-bold text-success">RM {{ number_format($selectedSale->total_price, 2) }}</td>
                                </tr>
                                <tr>
                                    <td class="fw-medium">Agent:</td>
                                    <td>{{ $selectedSale->user_name }}</td>
                                </tr>
                                <tr>
                                    <td class="fw-medium">Email:</td>
                                    <td>{{ $selectedSale->user_email }}</td>
                                </tr>
                                <tr>
                                    <td class="fw-medium">Role:</td>
                                    <td><span class="badge bg-primary">{{ $selectedSale->role_name ?? 'N/A' }}</span></td>
                                </tr>
                                <tr>
                                    <td class="fw-medium">Cooperative:</td>
                                    <td>{{ $selectedSale->cooperative_name ?? 'N/A' }}</td>
                                </tr>
                                <tr>
                                    <td class="fw-medium">Branch:</td>
                                    <td>{{ $selectedSale->branch_name ?? 'N/A' }}</td>
                                </tr>
                                <tr>
                                    <td class="fw-medium">Sale Date:</td>
                                    <td>{{ \Carbon\Carbon::parse($selectedSale->created_at)->format('M d, Y H:i') }}</td>
                                </tr>
                            </table>
                        </div>
                    </div>
                    
                    <div class="d-flex gap-3 justify-content-center mt-4">
                        <button type="button" class="btn btn-light px-4" wire:click="closeModal" style="border-radius: 10px; font-weight: 500;">
                            Close
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    @endif
</div>
